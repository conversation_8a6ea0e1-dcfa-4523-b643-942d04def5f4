# Channel高级使用模式

## Channel的基本概念回顾

### Channel类型
- **无缓冲channel**：同步通信，发送和接收必须同时准备好
- **有缓冲channel**：异步通信，缓冲区未满时发送不阻塞
- **单向channel**：只能发送或只能接收的channel

## 高级使用模式

### 1. Fan-out/Fan-in模式

#### Fan-out（扇出）
将一个输入分发给多个goroutine处理
```go
func fanOut(input <-chan int, workers int) []<-chan int {
    outputs := make([]<-chan int, workers)
    
    for i := 0; i < workers; i++ {
        output := make(chan int)
        outputs[i] = output
        
        go func(out chan<- int) {
            defer close(out)
            for data := range input {
                // 处理数据
                result := process(data)
                out <- result
            }
        }(output)
    }
    
    return outputs
}
```

#### Fan-in（扇入）
将多个输入合并到一个输出
```go
func fanIn(inputs ...<-chan int) <-chan int {
    output := make(chan int)
    var wg sync.WaitGroup
    
    // 为每个输入channel启动一个goroutine
    for _, input := range inputs {
        wg.Add(1)
        go func(ch <-chan int) {
            defer wg.Done()
            for data := range ch {
                output <- data
            }
        }(input)
    }
    
    // 等待所有输入完成后关闭输出
    go func() {
        wg.Wait()
        close(output)
    }()
    
    return output
}
```

### 2. Pipeline模式
```go
// 阶段1：生成数据
func generator(nums ...int) <-chan int {
    out := make(chan int)
    go func() {
        defer close(out)
        for _, n := range nums {
            out <- n
        }
    }()
    return out
}

// 阶段2：处理数据
func square(in <-chan int) <-chan int {
    out := make(chan int)
    go func() {
        defer close(out)
        for n := range in {
            out <- n * n
        }
    }()
    return out
}

// 阶段3：过滤数据
func filter(in <-chan int, predicate func(int) bool) <-chan int {
    out := make(chan int)
    go func() {
        defer close(out)
        for n := range in {
            if predicate(n) {
                out <- n
            }
        }
    }()
    return out
}

// 使用pipeline
func usePipeline() {
    // 构建pipeline
    numbers := generator(1, 2, 3, 4, 5)
    squared := square(numbers)
    filtered := filter(squared, func(n int) bool { return n > 10 })
    
    // 消费结果
    for result := range filtered {
        fmt.Println(result)
    }
}
```

### 3. Worker Pool模式
```go
type Job struct {
    ID   int
    Data interface{}
}

type Result struct {
    Job    Job
    Output interface{}
    Error  error
}

func workerPool(jobs <-chan Job, results chan<- Result, numWorkers int) {
    var wg sync.WaitGroup
    
    // 启动worker
    for i := 0; i < numWorkers; i++ {
        wg.Add(1)
        go func(workerID int) {
            defer wg.Done()
            for job := range jobs {
                // 处理任务
                output, err := processJob(job)
                results <- Result{
                    Job:    job,
                    Output: output,
                    Error:  err,
                }
            }
        }(i)
    }
    
    // 等待所有worker完成后关闭结果channel
    go func() {
        wg.Wait()
        close(results)
    }()
}

func processJob(job Job) (interface{}, error) {
    // 模拟处理时间
    time.Sleep(time.Millisecond * 100)
    return fmt.Sprintf("Processed job %d", job.ID), nil
}
```

### 4. 超时和取消模式
```go
func operationWithTimeout(timeout time.Duration) (string, error) {
    result := make(chan string, 1)
    
    go func() {
        // 模拟长时间运行的操作
        time.Sleep(2 * time.Second)
        result <- "操作完成"
    }()
    
    select {
    case res := <-result:
        return res, nil
    case <-time.After(timeout):
        return "", errors.New("操作超时")
    }
}

// 使用context的取消模式
func operationWithContext(ctx context.Context) (string, error) {
    result := make(chan string, 1)
    
    go func() {
        // 长时间运行的操作
        select {
        case <-time.After(2 * time.Second):
            result <- "操作完成"
        case <-ctx.Done():
            return // 操作被取消
        }
    }()
    
    select {
    case res := <-result:
        return res, nil
    case <-ctx.Done():
        return "", ctx.Err()
    }
}
```

### 5. 信号量模式
```go
// 使用channel实现信号量
type Semaphore chan struct{}

func NewSemaphore(capacity int) Semaphore {
    return make(Semaphore, capacity)
}

func (s Semaphore) Acquire() {
    s <- struct{}{}
}

func (s Semaphore) Release() {
    <-s
}

// 限制并发数量的示例
func limitedConcurrency() {
    sem := NewSemaphore(3) // 最多3个并发
    var wg sync.WaitGroup
    
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            
            sem.Acquire()
            defer sem.Release()
            
            // 执行需要限制并发的操作
            fmt.Printf("Worker %d 开始工作\n", id)
            time.Sleep(time.Second)
            fmt.Printf("Worker %d 完成工作\n", id)
        }(i)
    }
    
    wg.Wait()
}
```

### 6. 发布-订阅模式
```go
type PubSub struct {
    mu          sync.RWMutex
    subscribers map[string][]chan<- interface{}
    closed      bool
}

func NewPubSub() *PubSub {
    return &PubSub{
        subscribers: make(map[string][]chan<- interface{}),
    }
}

func (ps *PubSub) Subscribe(topic string) <-chan interface{} {
    ps.mu.Lock()
    defer ps.mu.Unlock()
    
    if ps.closed {
        return nil
    }
    
    ch := make(chan interface{}, 1)
    ps.subscribers[topic] = append(ps.subscribers[topic], ch)
    return ch
}

func (ps *PubSub) Publish(topic string, data interface{}) {
    ps.mu.RLock()
    defer ps.mu.RUnlock()
    
    if ps.closed {
        return
    }
    
    for _, ch := range ps.subscribers[topic] {
        select {
        case ch <- data:
        default:
            // 订阅者处理太慢，跳过
        }
    }
}

func (ps *PubSub) Close() {
    ps.mu.Lock()
    defer ps.mu.Unlock()
    
    if ps.closed {
        return
    }
    
    ps.closed = true
    for _, subscribers := range ps.subscribers {
        for _, ch := range subscribers {
            close(ch)
        }
    }
}
```

### 7. 请求-响应模式
```go
type Request struct {
    Data     interface{}
    Response chan Response
}

type Response struct {
    Result interface{}
    Error  error
}

type Server struct {
    requests chan Request
}

func NewServer() *Server {
    s := &Server{
        requests: make(chan Request),
    }
    
    go s.handleRequests()
    return s
}

func (s *Server) handleRequests() {
    for req := range s.requests {
        // 处理请求
        result, err := s.processRequest(req.Data)
        
        // 发送响应
        req.Response <- Response{
            Result: result,
            Error:  err,
        }
        close(req.Response)
    }
}

func (s *Server) processRequest(data interface{}) (interface{}, error) {
    // 模拟处理
    time.Sleep(100 * time.Millisecond)
    return fmt.Sprintf("Processed: %v", data), nil
}

func (s *Server) Call(data interface{}) (interface{}, error) {
    response := make(chan Response, 1)
    
    s.requests <- Request{
        Data:     data,
        Response: response,
    }
    
    resp := <-response
    return resp.Result, resp.Error
}
```

### 8. 心跳模式
```go
func heartbeat(ctx context.Context, interval time.Duration) <-chan struct{} {
    heartbeatCh := make(chan struct{})
    
    go func() {
        defer close(heartbeatCh)
        ticker := time.NewTicker(interval)
        defer ticker.Stop()
        
        for {
            select {
            case <-ticker.C:
                select {
                case heartbeatCh <- struct{}{}:
                case <-ctx.Done():
                    return
                }
            case <-ctx.Done():
                return
            }
        }
    }()
    
    return heartbeatCh
}

// 使用心跳检测服务状态
func monitorService(ctx context.Context) {
    heartbeatCh := heartbeat(ctx, time.Second)
    
    for {
        select {
        case <-heartbeatCh:
            fmt.Println("服务正常运行")
        case <-time.After(2 * time.Second):
            fmt.Println("警告：服务可能异常")
        case <-ctx.Done():
            fmt.Println("监控停止")
            return
        }
    }
}
```

## Channel的性能优化

### 1. 选择合适的缓冲区大小
```go
// 无缓冲：同步通信，适合确保数据被处理
unbuffered := make(chan int)

// 小缓冲：减少阻塞，适合生产消费速度相近的场景
smallBuffer := make(chan int, 10)

// 大缓冲：适合突发流量，但要注意内存使用
largeBuffer := make(chan int, 1000)
```

### 2. 避免channel泄漏
```go
func avoidChannelLeak() {
    ch := make(chan int, 1)
    
    go func() {
        defer close(ch) // 确保channel被关闭
        
        for i := 0; i < 10; i++ {
            select {
            case ch <- i:
            case <-time.After(time.Second):
                return // 超时退出
            }
        }
    }()
    
    // 消费数据
    for data := range ch {
        fmt.Println(data)
    }
}
```

### 3. 使用select优化
```go
func optimizedSelect(input <-chan int, output chan<- int) {
    for {
        select {
        case data, ok := <-input:
            if !ok {
                return
            }
            
            select {
            case output <- data * 2:
            default:
                // 输出channel满了，丢弃数据或其他处理
                log.Println("输出channel满，丢弃数据:", data)
            }
            
        case <-time.After(time.Minute):
            // 定期清理或维护
            performMaintenance()
        }
    }
}
```

## 面试要点总结

1. **设计模式**：掌握Fan-out/Fan-in、Pipeline、Worker Pool等常用模式
2. **并发控制**：理解如何使用channel实现信号量、超时、取消等机制
3. **性能优化**：了解缓冲区大小选择、避免泄漏等优化技巧
4. **实际应用**：能够根据具体场景选择合适的channel使用模式
5. **错误处理**：掌握channel关闭、异常处理等最佳实践
6. **与其他同步原语的对比**：理解channel与mutex、sync.WaitGroup等的使用场景差异
