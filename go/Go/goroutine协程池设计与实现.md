# Goroutine协程池设计与实现

## 为什么需要协程池？

### 问题背景
1. **资源消耗**：虽然goroutine很轻量，但大量创建仍会消耗内存和CPU
2. **调度开销**：过多的goroutine会增加调度器的负担
3. **资源控制**：需要限制并发数量，防止系统资源耗尽
4. **任务管理**：需要统一管理和监控goroutine的生命周期

### 协程池的优势
- 复用goroutine，减少创建销毁开销
- 控制并发数量，避免资源耗尽
- 提供任务队列，平滑处理突发流量
- 统一错误处理和监控

## 协程池的基本设计

### 核心组件
```go
type Pool struct {
    capacity    int           // 协程池容量
    running     int32         // 当前运行的goroutine数量
    workers     []*worker     // worker切片
    taskQueue   chan Task     // 任务队列
    workerQueue chan *worker  // 空闲worker队列
    closed      int32         // 池状态
    once        sync.Once     // 确保只关闭一次
    cond        *sync.Cond    // 条件变量
    mu          sync.Mutex    // 互斥锁
}

type Task func()

type worker struct {
    pool     *Pool
    taskChan chan Task
    lastUsed time.Time
}
```

### 基本实现
```go
func NewPool(capacity int) *Pool {
    return &Pool{
        capacity:    capacity,
        taskQueue:   make(chan Task, capacity*2),
        workerQueue: make(chan *worker, capacity),
        cond:        sync.NewCond(&sync.Mutex{}),
    }
}

func (p *Pool) Submit(task Task) error {
    if atomic.LoadInt32(&p.closed) == 1 {
        return errors.New("pool is closed")
    }
    
    select {
    case p.taskQueue <- task:
        return nil
    default:
        return errors.New("task queue is full")
    }
}

func (p *Pool) worker() {
    defer func() {
        atomic.AddInt32(&p.running, -1)
        p.cond.Signal()
    }()
    
    for {
        select {
        case task := <-p.taskQueue:
            if task != nil {
                task()
            }
        case <-time.After(time.Minute): // 空闲超时
            return
        }
    }
}
```

## 高级协程池设计

### 动态扩缩容
```go
type DynamicPool struct {
    *Pool
    minWorkers int
    maxWorkers int
    scaleUp    time.Duration
    scaleDown  time.Duration
    metrics    *PoolMetrics
}

type PoolMetrics struct {
    TasksSubmitted int64
    TasksCompleted int64
    TasksFailed    int64
    WorkersCreated int64
    WorkersDestroyed int64
}

func (dp *DynamicPool) autoScale() {
    ticker := time.NewTicker(dp.scaleUp)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            dp.checkAndScale()
        }
    }
}

func (dp *DynamicPool) checkAndScale() {
    queueLen := len(dp.taskQueue)
    running := atomic.LoadInt32(&dp.running)
    
    // 扩容条件：任务队列长度 > 运行中的worker数量
    if queueLen > int(running) && running < int32(dp.maxWorkers) {
        dp.addWorker()
    }
    
    // 缩容条件：空闲时间过长且超过最小worker数量
    if running > int32(dp.minWorkers) {
        dp.removeIdleWorkers()
    }
}
```

### 优先级任务队列
```go
type PriorityTask struct {
    Task     func()
    Priority int
    ID       string
}

type PriorityPool struct {
    *Pool
    priorityQueue *PriorityQueue
}

type PriorityQueue struct {
    tasks []*PriorityTask
    mu    sync.Mutex
}

func (pq *PriorityQueue) Push(task *PriorityTask) {
    pq.mu.Lock()
    defer pq.mu.Unlock()
    
    pq.tasks = append(pq.tasks, task)
    sort.Slice(pq.tasks, func(i, j int) bool {
        return pq.tasks[i].Priority > pq.tasks[j].Priority
    })
}

func (pq *PriorityQueue) Pop() *PriorityTask {
    pq.mu.Lock()
    defer pq.mu.Unlock()
    
    if len(pq.tasks) == 0 {
        return nil
    }
    
    task := pq.tasks[0]
    pq.tasks = pq.tasks[1:]
    return task
}
```

## 协程池的最佳实践

### 1. 合理设置池大小
```go
// 根据CPU核心数设置
func OptimalPoolSize() int {
    // CPU密集型任务
    cpuIntensive := runtime.NumCPU()
    
    // IO密集型任务
    ioIntensive := runtime.NumCPU() * 2
    
    // 混合型任务
    mixed := runtime.NumCPU() + 1
    
    return mixed
}
```

### 2. 任务超时处理
```go
func (p *Pool) SubmitWithTimeout(task Task, timeout time.Duration) error {
    ctx, cancel := context.WithTimeout(context.Background(), timeout)
    defer cancel()
    
    wrappedTask := func() {
        done := make(chan struct{})
        go func() {
            defer close(done)
            task()
        }()
        
        select {
        case <-done:
            // 任务完成
        case <-ctx.Done():
            // 任务超时
            log.Printf("Task timeout after %v", timeout)
        }
    }
    
    return p.Submit(wrappedTask)
}
```

### 3. 错误处理和恢复
```go
func (p *Pool) safeExecute(task Task) {
    defer func() {
        if r := recover(); r != nil {
            log.Printf("Task panic recovered: %v", r)
            // 记录错误指标
            atomic.AddInt64(&p.metrics.TasksFailed, 1)
        }
    }()
    
    task()
    atomic.AddInt64(&p.metrics.TasksCompleted, 1)
}
```

## 性能优化技巧

### 1. 减少锁竞争
```go
// 使用无锁队列
type LockFreeQueue struct {
    head unsafe.Pointer
    tail unsafe.Pointer
}

func (q *LockFreeQueue) Enqueue(task Task) {
    node := &queueNode{task: task}
    for {
        tail := (*queueNode)(atomic.LoadPointer(&q.tail))
        next := (*queueNode)(atomic.LoadPointer(&tail.next))
        
        if tail == (*queueNode)(atomic.LoadPointer(&q.tail)) {
            if next == nil {
                if atomic.CompareAndSwapPointer(&tail.next, nil, unsafe.Pointer(node)) {
                    atomic.CompareAndSwapPointer(&q.tail, unsafe.Pointer(tail), unsafe.Pointer(node))
                    break
                }
            } else {
                atomic.CompareAndSwapPointer(&q.tail, unsafe.Pointer(tail), unsafe.Pointer(next))
            }
        }
    }
}
```

### 2. 内存池复用
```go
var taskPool = sync.Pool{
    New: func() interface{} {
        return &TaskWrapper{}
    },
}

type TaskWrapper struct {
    Task     func()
    Callback func(error)
    Context  context.Context
}

func (p *Pool) SubmitWithWrapper(task func()) {
    wrapper := taskPool.Get().(*TaskWrapper)
    wrapper.Task = task
    
    p.Submit(func() {
        defer taskPool.Put(wrapper)
        wrapper.Task()
    })
}
```

## 监控和指标

### 关键指标
```go
type PoolStats struct {
    Capacity        int           `json:"capacity"`
    Running         int           `json:"running"`
    Idle            int           `json:"idle"`
    QueueLength     int           `json:"queue_length"`
    TasksSubmitted  int64         `json:"tasks_submitted"`
    TasksCompleted  int64         `json:"tasks_completed"`
    TasksFailed     int64         `json:"tasks_failed"`
    AverageWaitTime time.Duration `json:"average_wait_time"`
    AverageExecTime time.Duration `json:"average_exec_time"`
}

func (p *Pool) GetStats() PoolStats {
    return PoolStats{
        Capacity:       p.capacity,
        Running:        int(atomic.LoadInt32(&p.running)),
        QueueLength:    len(p.taskQueue),
        TasksSubmitted: atomic.LoadInt64(&p.metrics.TasksSubmitted),
        TasksCompleted: atomic.LoadInt64(&p.metrics.TasksCompleted),
        TasksFailed:    atomic.LoadInt64(&p.metrics.TasksFailed),
    }
}
```

## 常见问题和解决方案

### 1. 死锁问题
- **原因**：任务间相互等待，或者等待池中的其他任务
- **解决**：避免任务间依赖，使用超时机制

### 2. 内存泄漏
- **原因**：goroutine未正确退出，任务对象未释放
- **解决**：正确实现池的关闭机制，使用对象池

### 3. 性能瓶颈
- **原因**：锁竞争激烈，任务分配不均
- **解决**：使用无锁数据结构，实现工作窃取算法

## 面试要点总结

1. **设计原理**：理解协程池的核心组件和工作流程
2. **性能优化**：掌握减少锁竞争、内存复用等优化技巧
3. **错误处理**：了解panic恢复、超时处理等机制
4. **监控指标**：知道关键性能指标和监控方法
5. **最佳实践**：合理设置池大小、任务设计原则等
