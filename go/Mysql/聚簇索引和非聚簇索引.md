聚簇索引（Clustered Index）和非聚簇索引（Non-Clustered Index）是数据库管理系统中两种不同的索引类型，它们在数据的物理存储方式、查询效率、使用场景等方面都有显著的区别。

### 聚簇索引（Clustered Index）

**定义**：
- 聚簇索引是一种将表中的数据行与索引按顺序存储的索引类型。在一个表中，数据的物理存储顺序与聚簇索引的键值顺序相同。
  
**特点**：
- **唯一性**：每个表只能有一个聚簇索引，因为表的数据只能以一种物理顺序存储。
- **存储结构**：聚簇索引的叶子节点包含实际的数据行，而不是指向数据行的指针。这意味着通过聚簇索引查找数据时，不需要再进行额外的回表操作。
- **查询性能**：对于范围查询（如 `BETWEEN`、`<`、`>`）、排序操作，以及需要读取连续数据的查询，聚簇索引性能较高，因为数据物理上是连续存储的。
  
**应用场景**：
- 适用于频繁进行排序操作或范围查询的列，例如日期字段、自动递增的主键字段等。
- 在 MySQL 的 InnoDB 存储引擎中，主键通常被默认用作聚簇索引。

### 非聚簇索引（Non-Clustered Index）

**定义**：
- 非聚簇索引是一种独立于数据存储顺序的索引类型。非聚簇索引的叶子节点存储的是指向数据行的指针，而不是实际的数据行。
  
**特点**：
- **多个索引**：一个表可以有多个非聚簇索引，因为这些索引不会影响数据的物理存储顺序。
- **存储结构**：非聚簇索引的叶子节点包含索引键值和指向对应数据行的指针。这意味着通过非聚簇索引查找数据时，可能需要通过指针进行额外的回表操作来获取实际数据。
- **查询性能**：非聚簇索引在精确查找（如查找某个具体值）时性能较好，但对于需要大量回表的查询，性能可能不如聚簇索引。

**应用场景**：
- 适用于需要快速查找特定值的列，例如用户名、邮箱、唯一标识符等。
- 可以在需要多个查询条件时使用多个非聚簇索引。

### 聚簇索引与非聚簇索引的比较

- **数据存储**：
  - 聚簇索引：数据按索引键的顺序存储，叶子节点包含实际数据。
  - 非聚簇索引：数据存储顺序独立于索引，叶子节点包含指向数据的指针。
  
- **数量限制**：
  - 聚簇索引：一个表只能有一个。
  - 非聚簇索引：一个表可以有多个。
  
- **查询性能**：
  - 聚簇索引：适合范围查询、排序操作，因为数据物理上是连续存储的。
  - 非聚簇索引：适合精确查找，但对于范围查询和需要回表的操作性能较差。

- **空间开销**：
  - 聚簇索引：由于数据按索引存储，不需要额外的指针存储，节省空间。
  - 非聚簇索引：需要额外存储指针，空间开销更大。

### 总结

- **聚簇索引**：数据和索引是合二为一的，适合那些需要排序和范围查询的场景。
- **非聚簇索引**：数据和索引分离，适合快速精确查找的场景，可以创建多个，提供灵活的查询优化手段。