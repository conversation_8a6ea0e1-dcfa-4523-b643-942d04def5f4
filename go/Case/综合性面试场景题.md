# 综合性面试场景题

## 场景一：高并发Web服务器设计

### 问题描述
设计一个高并发的Web服务器，需要处理每秒10万个HTTP请求，每个请求可能涉及数据库查询、缓存操作和外部API调用。请从Go语言实现、并发控制、系统资源管理等角度给出完整的解决方案。

### 考查要点
- Go语言并发编程
- 系统资源管理
- 网络I/O优化
- 内存管理
- 负载均衡

### 参考答案

#### 1. 整体架构设计

```go
package main

import (
    "context"
    "fmt"
    "log"
    "net/http"
    "runtime"
    "sync"
    "time"
)

// 服务器配置
type ServerConfig struct {
    MaxWorkers      int           // 最大工作协程数
    MaxConnections  int           // 最大连接数
    ReadTimeout     time.Duration // 读超时
    WriteTimeout    time.Duration // 写超时
    IdleTimeout     time.Duration // 空闲超时
}

// 高并发Web服务器
type HighConcurrencyServer struct {
    config      ServerConfig
    workerPool  *WorkerPool
    connLimiter chan struct{}
    server      *http.Server
    
    // 监控指标
    activeConnections int64
    totalRequests     int64
    mu                sync.RWMutex
}

func NewHighConcurrencyServer(config ServerConfig) *HighConcurrencyServer {
    return &HighConcurrencyServer{
        config:      config,
        workerPool:  NewWorkerPool(config.MaxWorkers),
        connLimiter: make(chan struct{}, config.MaxConnections),
    }
}
```

#### 2. 工作池实现

```go
// 工作池
type WorkerPool struct {
    workers    int
    jobQueue   chan Job
    workerPool chan chan Job
    quit       chan bool
    wg         sync.WaitGroup
}

type Job struct {
    Request  *http.Request
    Response http.ResponseWriter
    Done     chan bool
}

func NewWorkerPool(maxWorkers int) *WorkerPool {
    pool := &WorkerPool{
        workers:    maxWorkers,
        jobQueue:   make(chan Job, maxWorkers*2),
        workerPool: make(chan chan Job, maxWorkers),
        quit:       make(chan bool),
    }
    
    pool.start()
    return pool
}

func (p *WorkerPool) start() {
    // 启动工作协程
    for i := 0; i < p.workers; i++ {
        worker := NewWorker(p.workerPool, p.quit)
        worker.start()
    }
    
    // 启动调度器
    go p.dispatch()
}

func (p *WorkerPool) dispatch() {
    for {
        select {
        case job := <-p.jobQueue:
            // 获取可用的工作协程
            go func() {
                jobChannel := <-p.workerPool
                jobChannel <- job
            }()
        case <-p.quit:
            return
        }
    }
}

// 工作协程
type Worker struct {
    workerPool chan chan Job
    jobChannel chan Job
    quit       chan bool
}

func NewWorker(workerPool chan chan Job, quit chan bool) *Worker {
    return &Worker{
        workerPool: workerPool,
        jobChannel: make(chan Job),
        quit:       quit,
    }
}

func (w *Worker) start() {
    go func() {
        for {
            // 将自己注册到工作池
            w.workerPool <- w.jobChannel
            
            select {
            case job := <-w.jobChannel:
                // 处理任务
                w.processJob(job)
            case <-w.quit:
                return
            }
        }
    }()
}

func (w *Worker) processJob(job Job) {
    defer func() {
        if r := recover(); r != nil {
            log.Printf("Worker panic: %v", r)
        }
        job.Done <- true
    }()
    
    // 模拟业务处理
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    // 并发处理多个操作
    var wg sync.WaitGroup
    results := make(chan string, 3)
    
    // 数据库查询
    wg.Add(1)
    go func() {
        defer wg.Done()
        result := queryDatabase(ctx, job.Request)
        results <- result
    }()
    
    // 缓存操作
    wg.Add(1)
    go func() {
        defer wg.Done()
        result := queryCache(ctx, job.Request)
        results <- result
    }()
    
    // 外部API调用
    wg.Add(1)
    go func() {
        defer wg.Done()
        result := callExternalAPI(ctx, job.Request)
        results <- result
    }()
    
    // 等待所有操作完成
    go func() {
        wg.Wait()
        close(results)
    }()
    
    // 收集结果
    var response []string
    for result := range results {
        response = append(response, result)
    }
    
    // 写入响应
    job.Response.Header().Set("Content-Type", "application/json")
    job.Response.WriteHeader(http.StatusOK)
    fmt.Fprintf(job.Response, `{"results": %v}`, response)
}
```

#### 3. 连接管理和限流

```go
func (s *HighConcurrencyServer) ServeHTTP(w http.ResponseWriter, r *http.Request) {
    // 连接限流
    select {
    case s.connLimiter <- struct{}{}:
        defer func() { <-s.connLimiter }()
    default:
        http.Error(w, "Server too busy", http.StatusServiceUnavailable)
        return
    }
    
    // 更新统计
    s.mu.Lock()
    s.activeConnections++
    s.totalRequests++
    s.mu.Unlock()
    
    defer func() {
        s.mu.Lock()
        s.activeConnections--
        s.mu.Unlock()
    }()
    
    // 提交任务到工作池
    job := Job{
        Request:  r,
        Response: w,
        Done:     make(chan bool, 1),
    }
    
    select {
    case s.workerPool.jobQueue <- job:
        // 等待任务完成
        <-job.Done
    case <-time.After(10 * time.Second):
        http.Error(w, "Request timeout", http.StatusRequestTimeout)
    }
}
```

#### 4. 系统优化建议

**操作系统层面**：
- 调整文件描述符限制：`ulimit -n 65536`
- 优化TCP参数：调整`net.core.somaxconn`
- 使用epoll模型提高I/O效率
- 设置CPU亲和性减少上下文切换

**Go语言层面**：
- 设置`GOMAXPROCS`匹配CPU核心数
- 使用对象池减少GC压力
- 优化内存分配模式
- 使用pprof进行性能分析

## 场景二：分布式缓存系统

### 问题描述
设计一个分布式缓存系统，支持数据分片、一致性哈希、故障转移等功能。要求支持高并发读写，数据一致性，以及节点动态扩缩容。

### 考查要点
- 分布式系统设计
- 一致性哈希算法
- 并发控制
- 故障处理
- 数据同步

### 参考答案

#### 1. 一致性哈希实现

```go
package main

import (
    "crypto/sha1"
    "fmt"
    "sort"
    "strconv"
    "sync"
)

// 一致性哈希环
type ConsistentHash struct {
    hash     func(data []byte) uint32
    replicas int               // 虚拟节点数
    keys     []int            // 哈希环上的点
    hashMap  map[int]string   // 虚拟节点到真实节点的映射
    mu       sync.RWMutex
}

func NewConsistentHash(replicas int, fn func([]byte) uint32) *ConsistentHash {
    ch := &ConsistentHash{
        replicas: replicas,
        hash:     fn,
        hashMap:  make(map[int]string),
    }
    if ch.hash == nil {
        ch.hash = crc32Hash
    }
    return ch
}

func crc32Hash(data []byte) uint32 {
    h := sha1.New()
    h.Write(data)
    hash := h.Sum(nil)
    return uint32(hash[0])<<24 | uint32(hash[1])<<16 | 
           uint32(hash[2])<<8 | uint32(hash[3])
}

// 添加节点
func (ch *ConsistentHash) Add(keys ...string) {
    ch.mu.Lock()
    defer ch.mu.Unlock()
    
    for _, key := range keys {
        for i := 0; i < ch.replicas; i++ {
            hash := int(ch.hash([]byte(strconv.Itoa(i) + key)))
            ch.keys = append(ch.keys, hash)
            ch.hashMap[hash] = key
        }
    }
    sort.Ints(ch.keys)
}

// 获取节点
func (ch *ConsistentHash) Get(key string) string {
    ch.mu.RLock()
    defer ch.mu.RUnlock()
    
    if len(ch.keys) == 0 {
        return ""
    }
    
    hash := int(ch.hash([]byte(key)))
    
    // 二分查找第一个大于等于hash的节点
    idx := sort.Search(len(ch.keys), func(i int) bool {
        return ch.keys[i] >= hash
    })
    
    // 如果没找到，使用第一个节点（环形）
    if idx == len(ch.keys) {
        idx = 0
    }
    
    return ch.hashMap[ch.keys[idx]]
}
```

#### 2. 分布式缓存节点

```go
// 缓存节点
type CacheNode struct {
    addr     string
    data     map[string][]byte
    mu       sync.RWMutex
    peers    *ConsistentHash
    clients  map[string]*http.Client
    
    // 故障检测
    healthy  bool
    lastPing time.Time
}

func NewCacheNode(addr string) *CacheNode {
    return &CacheNode{
        addr:    addr,
        data:    make(map[string][]byte),
        peers:   NewConsistentHash(50, nil),
        clients: make(map[string]*http.Client),
        healthy: true,
    }
}

// 本地存储操作
func (cn *CacheNode) Set(key string, value []byte) error {
    cn.mu.Lock()
    defer cn.mu.Unlock()
    cn.data[key] = value
    return nil
}

func (cn *CacheNode) Get(key string) ([]byte, bool) {
    cn.mu.RLock()
    defer cn.mu.RUnlock()
    value, exists := cn.data[key]
    return value, exists
}

// 分布式操作
func (cn *CacheNode) DistributedSet(key string, value []byte) error {
    // 确定负责的节点
    targetNode := cn.peers.Get(key)
    
    if targetNode == cn.addr {
        // 本地存储
        return cn.Set(key, value)
    }
    
    // 远程存储
    return cn.remoteSet(targetNode, key, value)
}

func (cn *CacheNode) DistributedGet(key string) ([]byte, error) {
    targetNode := cn.peers.Get(key)
    
    if targetNode == cn.addr {
        // 本地获取
        if value, exists := cn.Get(key); exists {
            return value, nil
        }
        return nil, fmt.Errorf("key not found")
    }
    
    // 远程获取
    return cn.remoteGet(targetNode, key)
}

// 远程操作实现
func (cn *CacheNode) remoteSet(addr, key string, value []byte) error {
    client := cn.getClient(addr)
    
    // 构造HTTP请求
    url := fmt.Sprintf("http://%s/set?key=%s", addr, key)
    resp, err := client.Post(url, "application/octet-stream", 
                            bytes.NewReader(value))
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    
    if resp.StatusCode != http.StatusOK {
        return fmt.Errorf("remote set failed: %d", resp.StatusCode)
    }
    
    return nil
}

func (cn *CacheNode) remoteGet(addr, key string) ([]byte, error) {
    client := cn.getClient(addr)
    
    url := fmt.Sprintf("http://%s/get?key=%s", addr, key)
    resp, err := client.Get(url)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    if resp.StatusCode == http.StatusNotFound {
        return nil, fmt.Errorf("key not found")
    }
    
    if resp.StatusCode != http.StatusOK {
        return nil, fmt.Errorf("remote get failed: %d", resp.StatusCode)
    }
    
    return ioutil.ReadAll(resp.Body)
}
```

#### 3. 故障检测和恢复

```go
// 健康检查
func (cn *CacheNode) startHealthCheck() {
    ticker := time.NewTicker(5 * time.Second)
    go func() {
        for range ticker.C {
            cn.checkPeersHealth()
        }
    }()
}

func (cn *CacheNode) checkPeersHealth() {
    for addr := range cn.clients {
        if addr == cn.addr {
            continue
        }
        
        go func(peerAddr string) {
            client := cn.getClient(peerAddr)
            
            ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
            defer cancel()
            
            req, _ := http.NewRequestWithContext(ctx, "GET", 
                fmt.Sprintf("http://%s/ping", peerAddr), nil)
            
            resp, err := client.Do(req)
            if err != nil || resp.StatusCode != http.StatusOK {
                cn.handleNodeFailure(peerAddr)
            } else {
                cn.handleNodeRecovery(peerAddr)
            }
            
            if resp != nil {
                resp.Body.Close()
            }
        }(addr)
    }
}

func (cn *CacheNode) handleNodeFailure(addr string) {
    log.Printf("Node %s failed, removing from cluster", addr)
    
    // 从一致性哈希环中移除
    cn.peers.Remove(addr)
    
    // 数据迁移（简化实现）
    cn.migrateDataFromFailedNode(addr)
}

func (cn *CacheNode) handleNodeRecovery(addr string) {
    log.Printf("Node %s recovered, adding back to cluster", addr)
    cn.peers.Add(addr)
}
```

## 场景三：实时日志处理系统

### 问题描述
设计一个实时日志处理系统，需要处理每秒百万级别的日志数据，支持实时分析、告警、存储等功能。要求低延迟、高吞吐量、可扩展。

### 考查要点
- 流式数据处理
- 内存管理
- I/O优化
- 并发编程
- 系统监控

### 参考答案

#### 1. 日志处理管道

```go
// 日志处理器
type LogProcessor struct {
    inputChan    chan LogEntry
    outputChans  []chan ProcessedLog
    workers      int
    batchSize    int
    flushInterval time.Duration
    
    // 统计信息
    processedCount int64
    errorCount     int64
    
    ctx    context.Context
    cancel context.CancelFunc
    wg     sync.WaitGroup
}

type LogEntry struct {
    Timestamp time.Time
    Level     string
    Message   string
    Source    string
    Fields    map[string]interface{}
}

type ProcessedLog struct {
    Original  LogEntry
    Parsed    map[string]interface{}
    Metrics   map[string]float64
    Alerts    []Alert
}

func NewLogProcessor(workers, batchSize int, flushInterval time.Duration) *LogProcessor {
    ctx, cancel := context.WithCancel(context.Background())
    
    return &LogProcessor{
        inputChan:     make(chan LogEntry, batchSize*workers),
        outputChans:   make([]chan ProcessedLog, 0),
        workers:       workers,
        batchSize:     batchSize,
        flushInterval: flushInterval,
        ctx:           ctx,
        cancel:        cancel,
    }
}

func (lp *LogProcessor) Start() {
    // 启动工作协程
    for i := 0; i < lp.workers; i++ {
        lp.wg.Add(1)
        go lp.worker(i)
    }
    
    // 启动批处理协程
    lp.wg.Add(1)
    go lp.batchProcessor()
}

func (lp *LogProcessor) worker(id int) {
    defer lp.wg.Done()
    
    batch := make([]LogEntry, 0, lp.batchSize)
    ticker := time.NewTicker(lp.flushInterval)
    defer ticker.Stop()
    
    for {
        select {
        case entry := <-lp.inputChan:
            batch = append(batch, entry)
            
            if len(batch) >= lp.batchSize {
                lp.processBatch(batch)
                batch = batch[:0] // 重置切片
            }
            
        case <-ticker.C:
            if len(batch) > 0 {
                lp.processBatch(batch)
                batch = batch[:0]
            }
            
        case <-lp.ctx.Done():
            // 处理剩余数据
            if len(batch) > 0 {
                lp.processBatch(batch)
            }
            return
        }
    }
}

func (lp *LogProcessor) processBatch(batch []LogEntry) {
    processed := make([]ProcessedLog, 0, len(batch))
    
    for _, entry := range batch {
        // 并行处理单个日志
        result := lp.processLogEntry(entry)
        processed = append(processed, result)
        
        atomic.AddInt64(&lp.processedCount, 1)
    }
    
    // 分发到输出通道
    lp.distributeResults(processed)
}

func (lp *LogProcessor) processLogEntry(entry LogEntry) ProcessedLog {
    result := ProcessedLog{
        Original: entry,
        Parsed:   make(map[string]interface{}),
        Metrics:  make(map[string]float64),
        Alerts:   make([]Alert, 0),
    }
    
    // 解析日志
    lp.parseLog(&result)
    
    // 提取指标
    lp.extractMetrics(&result)
    
    // 检查告警
    lp.checkAlerts(&result)
    
    return result
}
```

#### 4. 性能监控

```go
// 性能监控
type PerformanceMonitor struct {
    metrics map[string]*Metric
    mu      sync.RWMutex
}

type Metric struct {
    Count     int64
    Sum       float64
    Min       float64
    Max       float64
    LastValue float64
    Timestamp time.Time
}

func (pm *PerformanceMonitor) Record(name string, value float64) {
    pm.mu.Lock()
    defer pm.mu.Unlock()
    
    metric, exists := pm.metrics[name]
    if !exists {
        metric = &Metric{
            Min: value,
            Max: value,
        }
        pm.metrics[name] = metric
    }
    
    metric.Count++
    metric.Sum += value
    metric.LastValue = value
    metric.Timestamp = time.Now()
    
    if value < metric.Min {
        metric.Min = value
    }
    if value > metric.Max {
        metric.Max = value
    }
}

func (pm *PerformanceMonitor) GetMetrics() map[string]*Metric {
    pm.mu.RLock()
    defer pm.mu.RUnlock()
    
    result := make(map[string]*Metric)
    for k, v := range pm.metrics {
        result[k] = &Metric{
            Count:     v.Count,
            Sum:       v.Sum,
            Min:       v.Min,
            Max:       v.Max,
            LastValue: v.LastValue,
            Timestamp: v.Timestamp,
        }
    }
    return result
}
```

## 总结

这些综合性面试场景题涵盖了：

1. **Go语言特性**：goroutine、channel、context、sync包等
2. **并发编程**：工作池、管道模式、并发控制等
3. **系统设计**：分布式架构、一致性哈希、故障处理等
4. **性能优化**：内存管理、I/O优化、批处理等
5. **操作系统知识**：进程调度、内存管理、网络I/O等

通过这些场景题的练习，可以全面提升系统设计和编程能力。
