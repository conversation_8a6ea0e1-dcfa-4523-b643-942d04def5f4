# Go语言面试快速指南 🚀

> **⚡ 面试前30分钟救急手册 - 快速定位核心考点！**

## 📁 目录结构速查

```
go/
├── 🚀 快速面试指南.md              # 本文件 - 面试救急手册
├── 📖 面试题索引与学习路径.md      # 系统化学习路径
├── 📖 README.md                   # 项目总览
│
├── 🐹 Go/                         # Go语言核心特性
├── ⚡ Concurrency/                # 并发编程 (已整理)
├── 🖥️ OS/                        # 操作系统 (已整理)
├── 🌐 Network/ & NetworkIO/       # 网络编程
├── 🗄️ MySQL/ & Redis/ & MongoDB/  # 数据库技术栈
├── 🔗 Distributed/               # 分布式系统 (已整理)
├── 🧮 Algorithm/                  # 算法 (已整理)
├── 📊 DataStructures/             # 数据结构
├── 📚 Theory/                     # 理论基础
├── 💼 Case/                      # 实战案例
├── 🔒 Security/                   # 安全相关
├── 🏗️ SystemDesign/              # 系统设计
└── 📨 Kafka/                     # 消息队列
```

## 🎯 使用说明

本指南按照**面试频率**和**重要程度**整理，帮您在有限时间内快速复习最关键的内容。

**使用建议**：
- 🔥 **面试前1小时**：重点看"必考核心"部分
- ⏰ **面试前30分钟**：快速浏览"高频问题"
- 📱 **候场时间**：查看"一句话总结"

---

## 🔥 必考核心 (90%会问到)

### 1. Goroutine与并发 ⭐⭐⭐⭐⭐

#### 核心问题
- **GPM调度模型是什么？**
  - G: Goroutine用户级线程
  - P: Processor逻辑处理器，维护本地队列
  - M: Machine操作系统线程
  - [详细解答](并发与多线程/Goroutine深度解析/GPM调度模型详解.md)

- **Goroutine什么时候会阻塞？**
  - Channel操作、网络I/O、系统调用、锁等待
  - [详细解答](Go/goroutine什么情况下会阻塞.md)

- **如何检测Goroutine泄漏？**
  - runtime.NumGoroutine()、pprof工具
  - [详细解答](Go/goroutine泄漏检测与防范.md)

#### 一句话总结
> GPM是Go调度器核心，G在P的本地队列中被M执行，工作窃取保证负载均衡

### 2. Channel通信机制 ⭐⭐⭐⭐⭐

#### 核心问题
- **Channel底层实现原理？**
  - 环形缓冲区 + 等待队列 + 互斥锁
  - [详细解答](Go/chan底层原理.md)

- **有缓冲和无缓冲Channel区别？**
  - 无缓冲：同步通信，发送方阻塞直到接收
  - 有缓冲：异步通信，缓冲区满才阻塞
  - [详细解答](Concurrency/Channel深度解析与最佳实践.md)

#### 一句话总结
> Channel是Go的CSP模型实现，通过通信来共享内存而不是共享内存来通信

### 3. 内存管理与GC ⭐⭐⭐⭐

#### 核心问题
- **Go的GC算法是什么？**
  - 三色标记 + 并发清除 + 写屏障
  - [详细解答](Go/GC垃圾回收算法.md)

- **什么是内存逃逸？**
  - 变量从栈逃逸到堆，通过逃逸分析决定
  - [详细解答](Go/Go语言高级特性与性能优化.md)

#### 一句话总结
> Go使用三色标记GC，逃逸分析决定变量分配在栈还是堆

---

## ⚡ 高频问题 (70%会问到)

### 4. 数据结构底层 ⭐⭐⭐⭐

#### Slice
- **底层结构**：指针 + 长度 + 容量
- **扩容机制**：容量翻倍或1.25倍增长
- [详细解答](Go/slice实践以及底层实现.md)

#### Map
- **底层实现**：哈希表 + 桶结构
- **并发安全**：不安全，需要sync.Map或加锁
- [详细解答](Go/map底层实现和并发安全.md)

#### Interface
- **内部结构**：类型信息 + 数据指针
- **动态分发**：通过方法表实现
- [详细解答](Go/Interface内部实现的理解.md)

### 5. 同步原语 ⭐⭐⭐

#### Mutex
- **实现原理**：CAS + 信号量
- **公平性**：正常模式 vs 饥饿模式
- [详细解答](Go/互斥锁实现原理剖析.md)

#### 原子操作
- **适用场景**：简单数值操作、指针更新
- **性能优势**：无锁，避免上下文切换
- [详细解答](Concurrency/Go内存模型与原子操作.md)

---

## 🏢 按公司类型分类

### 🏢 互联网大厂 (字节/腾讯/阿里)

#### 必问题目
1. **GPM调度模型详解** - 90%会问
2. **GC三色标记算法** - 85%会问  
3. **Channel底层实现** - 80%会问
4. **高并发系统设计** - 75%会问

#### 加分项目
- 能画出GPM调度流程图
- 能解释GC的STW优化
- 能设计高性能并发组件
- 有大规模系统经验

### 🏪 中小型公司

#### 重点题目
1. **Goroutine基础使用** - 80%会问
2. **Channel通信模式** - 70%会问
3. **常见并发问题** - 65%会问
4. **性能优化经验** - 60%会问

#### 实用技能
- 能快速定位并发问题
- 熟悉性能分析工具
- 有实际项目经验

---

## 📊 操作系统核心

### 进程与线程 ⭐⭐⭐⭐

#### 必考问题
- **进程和线程的区别？**
  - 进程：资源分配单位，独立地址空间
  - 线程：调度执行单位，共享地址空间
  - [详细解答](OperatingSystem/进程和线程之间有什么区别.md)

- **进程调度算法有哪些？**
  - FCFS、SJF、RR、优先级调度
  - [详细解答](操作系统核心/进程与线程管理/进程调度算法.md)

### 内存管理 ⭐⭐⭐⭐

#### 核心概念
- **虚拟内存的作用？**
  - 地址空间隔离、内存保护、按需分配
  - [详细解答](操作系统核心/内存管理系统/物理内存与虚拟内存.md)

- **页面置换算法？**
  - FIFO、LRU、时钟算法
  - [详细解答](OperatingSystem/内存管理深度解析.md)

---

## 🌐 网络编程

### TCP/UDP ⭐⭐⭐

#### 经典问题
- **TCP三次握手四次挥手？**
  - 建立连接确保双方通信能力
  - 断开连接确保数据完整传输
  - [详细解答](Network/TCP三次握手以及四次挥手的流程.md)

- **TCP如何保证可靠性？**
  - 序列号、确认应答、重传机制、流量控制
  - [详细解答](Network/TCP可靠传输的保证.md)

### HTTP ⭐⭐⭐

#### 版本差异
- **HTTP/1.1 vs HTTP/2？**
  - 多路复用、头部压缩、服务器推送
  - [详细解答](Network/HTTP1.0，1.1，2.0的区别.md)

---

## 🗄️ 数据库

### MySQL ⭐⭐⭐⭐

#### 索引相关
- **B+树为什么适合做索引？**
  - 磁盘友好、范围查询高效、高度低
  - [详细解答](Mysql/为什么MySQL数据库索引选择使用B+树.md)

- **什么情况下索引失效？**
  - 函数操作、类型转换、前缀匹配
  - [详细解答](Mysql/什么情况下会导致索引失效.md)

#### 事务相关
- **ACID特性？**
  - 原子性、一致性、隔离性、持久性
  - [详细解答](Mysql/事务特性.md)

- **隔离级别有哪些？**
  - 读未提交、读已提交、可重复读、串行化
  - [详细解答](Mysql/四种事务隔离级别.md)

### Redis ⭐⭐⭐

#### 数据结构
- **Redis五大数据类型？**
  - String、Hash、List、Set、ZSet
  - [详细解答](Redis/redis的五大数据类型实现原理.md)

#### 持久化
- **RDB vs AOF？**
  - RDB：快照，恢复快但可能丢数据
  - AOF：日志，安全但文件大
  - [详细解答](Redis/持久化策略RDB和AOF.md)

---

## 🚨 最后30分钟冲刺

### 🔥 必背金句

1. **Go并发**："不要通过共享内存来通信，而要通过通信来共享内存"
2. **GPM调度**："G在P的本地队列中被M执行，工作窃取实现负载均衡"
3. **GC算法**："三色标记并发清除，写屏障保证并发安全"
4. **Channel**："有缓冲异步通信，无缓冲同步通信"
5. **虚拟内存**："提供抽象、保护、共享、虚拟化四大功能"

### ⚡ 快速检查清单

- [ ] 能画出GPM调度模型图
- [ ] 能解释Channel底层结构
- [ ] 能说出GC三色标记过程
- [ ] 能对比不同调度算法
- [ ] 能解释TCP握手挥手
- [ ] 能说出MySQL索引原理
- [ ] 能对比Redis持久化方案

### 🎯 面试技巧

1. **先总后分**：先说核心概念，再展开细节
2. **画图说明**：复杂概念用图表达更清晰
3. **举例验证**：用具体例子证明理解深度
4. **对比分析**：说出不同方案的优缺点
5. **实战经验**：结合项目经验回答问题

---

## 📱 移动端速查

### 一分钟速记

```
GPM = G(goroutine) + P(processor) + M(machine)
Channel = 环形缓冲区 + 等待队列 + 锁
GC = 三色标记 + 并发清除 + 写屏障
TCP = 三次握手建连 + 四次挥手断连
MySQL = B+树索引 + MVCC事务
Redis = 五种数据类型 + RDB/AOF持久化
```

### 关键数字

```
- Goroutine初始栈：2KB
- Go默认GOMAXPROCS：CPU核数
- TCP默认端口范围：1024-65535
- MySQL默认页大小：16KB
- Redis默认端口：6379
- HTTP默认端口：80/443
```

---

**🎯 记住：面试不是考试，是展示您解决问题的能力！**

**💪 相信自己，您已经准备充分了！加油！**
