# Linux信号处理机制

## 信号基础概念

### 1. 什么是信号？

信号是Linux系统中进程间通信的一种机制，用于通知进程发生了某种事件。信号是异步的，可以在任何时候发送给进程。

### 2. 信号的特点
- **异步性**：信号可以在任何时候到达
- **简单性**：信号只能传递很少的信息
- **不可靠性**：信号可能丢失（传统信号）
- **不可排队**：同类型信号不会排队

### 3. 信号分类

#### 标准信号（1-31）
```c
#include <signal.h>

// 常用标准信号
#define SIGHUP    1   // 挂起信号
#define SIGINT    2   // 中断信号 (Ctrl+C)
#define SIGQUIT   3   // 退出信号 (Ctrl+\)
#define SIGILL    4   // 非法指令
#define SIGABRT   6   // 异常终止
#define SIGFPE    8   // 浮点异常
#define SIGKILL   9   // 强制终止（不可捕获）
#define SIGSEGV   11  // 段错误
#define SIGPIPE   13  // 管道破裂
#define SIGALRM   14  // 定时器信号
#define SIGTERM   15  // 终止信号
#define SIGUSR1   10  // 用户自定义信号1
#define SIGUSR2   12  // 用户自定义信号2
#define SIGCHLD   17  // 子进程状态改变
#define SIGCONT   18  // 继续执行
#define SIGSTOP   19  // 停止执行（不可捕获）
#define SIGTSTP   20  // 终端停止 (Ctrl+Z)
```

#### 实时信号（32-64）
```c
// 实时信号范围
#define SIGRTMIN  32
#define SIGRTMAX  64

// 实时信号特点：
// 1. 可靠：不会丢失
// 2. 可排队：同类型信号会排队
// 3. 可携带数据：可以传递额外信息
```

## 信号处理方式

### 1. 默认处理

#### 默认动作类型
```c
// 信号的默认动作
typedef enum {
    SIG_DFL_TERM,    // 终止进程
    SIG_DFL_IGNORE,  // 忽略信号
    SIG_DFL_CORE,    // 终止进程并生成core文件
    SIG_DFL_STOP,    // 停止进程
    SIG_DFL_CONT     // 继续进程
} sig_default_action_t;

// 查看信号默认动作
void print_signal_info() {
    printf("SIGINT默认动作: 终止进程\n");
    printf("SIGCHLD默认动作: 忽略\n");
    printf("SIGQUIT默认动作: 终止进程并生成core\n");
    printf("SIGSTOP默认动作: 停止进程\n");
    printf("SIGCONT默认动作: 继续进程\n");
}
```

### 2. 忽略信号

#### 使用signal()函数忽略
```c
#include <signal.h>

void ignore_signals() {
    // 忽略SIGINT信号
    if (signal(SIGINT, SIG_IGN) == SIG_ERR) {
        perror("signal");
        exit(1);
    }
    
    // 忽略SIGPIPE信号（常用于网络编程）
    signal(SIGPIPE, SIG_IGN);
    
    printf("SIGINT和SIGPIPE信号已被忽略\n");
    
    // 注意：SIGKILL和SIGSTOP不能被忽略
    // signal(SIGKILL, SIG_IGN);  // 这会失败
}
```

### 3. 自定义处理函数

#### 简单信号处理器
```c
#include <signal.h>
#include <stdio.h>
#include <unistd.h>

volatile sig_atomic_t signal_received = 0;

// 信号处理函数
void signal_handler(int sig) {
    switch (sig) {
        case SIGINT:
            printf("\n收到SIGINT信号，准备退出...\n");
            signal_received = 1;
            break;
        case SIGUSR1:
            printf("收到SIGUSR1信号\n");
            break;
        case SIGUSR2:
            printf("收到SIGUSR2信号\n");
            break;
        default:
            printf("收到未知信号: %d\n", sig);
    }
}

int main() {
    // 注册信号处理函数
    signal(SIGINT, signal_handler);
    signal(SIGUSR1, signal_handler);
    signal(SIGUSR2, signal_handler);
    
    printf("进程PID: %d\n", getpid());
    printf("发送信号测试: kill -USR1 %d\n", getpid());
    
    // 主循环
    while (!signal_received) {
        printf("程序运行中...\n");
        sleep(2);
    }
    
    printf("程序正常退出\n");
    return 0;
}
```

#### 高级信号处理（sigaction）
```c
#include <signal.h>
#include <string.h>

struct sigaction old_action;

void advanced_signal_handler(int sig, siginfo_t *info, void *context) {
    printf("信号 %d 来自进程 %d\n", sig, info->si_pid);
    
    switch (sig) {
        case SIGINT:
            printf("接收到中断信号\n");
            break;
        case SIGCHLD:
            printf("子进程 %d 状态改变，退出状态: %d\n", 
                   info->si_pid, info->si_status);
            break;
        case SIGRTMIN:
            printf("实时信号，携带数据: %d\n", info->si_value.sival_int);
            break;
    }
}

void setup_advanced_signal_handling() {
    struct sigaction sa;
    
    // 清零结构体
    memset(&sa, 0, sizeof(sa));
    
    // 设置信号处理函数
    sa.sa_sigaction = advanced_signal_handler;
    
    // 设置标志
    sa.sa_flags = SA_SIGINFO | SA_RESTART;
    
    // 设置信号掩码（处理信号时阻塞的信号）
    sigemptyset(&sa.sa_mask);
    sigaddset(&sa.sa_mask, SIGINT);
    
    // 注册信号处理器
    if (sigaction(SIGINT, &sa, &old_action) == -1) {
        perror("sigaction");
        exit(1);
    }
    
    if (sigaction(SIGCHLD, &sa, NULL) == -1) {
        perror("sigaction");
        exit(1);
    }
    
    if (sigaction(SIGRTMIN, &sa, NULL) == -1) {
        perror("sigaction");
        exit(1);
    }
}
```

## 信号发送

### 1. kill()系统调用

#### 基本用法
```c
#include <signal.h>
#include <sys/types.h>

// 发送信号给指定进程
int send_signal_to_process(pid_t pid, int sig) {
    if (kill(pid, sig) == -1) {
        perror("kill");
        return -1;
    }
    printf("信号 %d 已发送给进程 %d\n", sig, pid);
    return 0;
}

// 发送信号给进程组
int send_signal_to_group(pid_t pgid, int sig) {
    // 负数表示进程组ID
    if (kill(-pgid, sig) == -1) {
        perror("kill");
        return -1;
    }
    printf("信号 %d 已发送给进程组 %d\n", sig, pgid);
    return 0;
}

// 检查进程是否存在
int check_process_exists(pid_t pid) {
    // 发送0信号不会实际发送，只检查权限和进程存在性
    if (kill(pid, 0) == 0) {
        printf("进程 %d 存在\n", pid);
        return 1;
    } else {
        printf("进程 %d 不存在或无权限\n", pid);
        return 0;
    }
}
```

### 2. 实时信号发送

#### sigqueue()函数
```c
#include <signal.h>

// 发送实时信号并携带数据
int send_realtime_signal(pid_t pid, int sig, int value) {
    union sigval sv;
    sv.sival_int = value;
    
    if (sigqueue(pid, sig, sv) == -1) {
        perror("sigqueue");
        return -1;
    }
    
    printf("实时信号 %d 已发送给进程 %d，携带数据: %d\n", 
           sig, pid, value);
    return 0;
}

// 示例：父子进程通信
void parent_child_communication() {
    pid_t pid = fork();
    
    if (pid == 0) {
        // 子进程
        struct sigaction sa;
        sa.sa_sigaction = advanced_signal_handler;
        sa.sa_flags = SA_SIGINFO;
        sigemptyset(&sa.sa_mask);
        sigaction(SIGRTMIN, &sa, NULL);
        
        printf("子进程等待信号...\n");
        pause(); // 等待信号
        exit(0);
    } else {
        // 父进程
        sleep(1); // 确保子进程准备好
        
        // 发送多个实时信号
        for (int i = 1; i <= 5; i++) {
            send_realtime_signal(pid, SIGRTMIN, i * 10);
            usleep(100000); // 100ms延迟
        }
        
        wait(NULL); // 等待子进程结束
    }
}
```

### 3. 定时器信号

#### alarm()函数
```c
#include <unistd.h>
#include <signal.h>

volatile int alarm_fired = 0;

void alarm_handler(int sig) {
    alarm_fired = 1;
    printf("定时器到期！\n");
}

void simple_timer_example() {
    signal(SIGALRM, alarm_handler);
    
    printf("设置5秒定时器\n");
    alarm(5);
    
    // 等待定时器到期
    while (!alarm_fired) {
        printf("等待中...\n");
        sleep(1);
    }
    
    printf("定时器处理完成\n");
}
```

#### setitimer()高精度定时器
```c
#include <sys/time.h>

void precision_timer_handler(int sig) {
    static int count = 0;
    printf("定时器触发 %d 次\n", ++count);
    
    if (count >= 10) {
        printf("定时器停止\n");
        alarm(0); // 停止定时器
    }
}

void precision_timer_example() {
    struct itimerval timer;
    
    signal(SIGALRM, precision_timer_handler);
    
    // 设置定时器：首次延迟1秒，之后每0.5秒触发一次
    timer.it_value.tv_sec = 1;      // 首次延迟
    timer.it_value.tv_usec = 0;
    timer.it_interval.tv_sec = 0;   // 间隔时间
    timer.it_interval.tv_usec = 500000; // 500ms
    
    if (setitimer(ITIMER_REAL, &timer, NULL) == -1) {
        perror("setitimer");
        return;
    }
    
    // 等待定时器事件
    while (1) {
        pause(); // 等待信号
    }
}
```

## 信号掩码和阻塞

### 1. 信号集操作

#### 信号集基本操作
```c
#include <signal.h>

void signal_set_operations() {
    sigset_t set, oldset;
    
    // 初始化空信号集
    sigemptyset(&set);
    
    // 初始化满信号集
    sigset_t fullset;
    sigfillset(&fullset);
    
    // 添加信号到集合
    sigaddset(&set, SIGINT);
    sigaddset(&set, SIGUSR1);
    sigaddset(&set, SIGUSR2);
    
    // 从集合中删除信号
    sigdelset(&set, SIGUSR2);
    
    // 检查信号是否在集合中
    if (sigismember(&set, SIGINT)) {
        printf("SIGINT在信号集中\n");
    }
    
    // 阻塞信号集中的信号
    if (sigprocmask(SIG_BLOCK, &set, &oldset) == -1) {
        perror("sigprocmask");
        return;
    }
    
    printf("SIGINT和SIGUSR1已被阻塞\n");
    
    // 执行一些操作...
    sleep(5);
    
    // 恢复原来的信号掩码
    if (sigprocmask(SIG_SETMASK, &oldset, NULL) == -1) {
        perror("sigprocmask");
        return;
    }
    
    printf("信号掩码已恢复\n");
}
```

### 2. 临界区保护

#### 保护临界区代码
```c
volatile int critical_data = 0;
sigset_t block_set;

void initialize_signal_blocking() {
    sigemptyset(&block_set);
    sigaddset(&block_set, SIGINT);
    sigaddset(&block_set, SIGUSR1);
}

void critical_section() {
    sigset_t old_set;
    
    // 进入临界区前阻塞信号
    sigprocmask(SIG_BLOCK, &block_set, &old_set);
    
    printf("进入临界区\n");
    
    // 临界区代码
    critical_data++;
    printf("修改临界数据: %d\n", critical_data);
    sleep(2); // 模拟耗时操作
    
    printf("离开临界区\n");
    
    // 离开临界区后恢复信号掩码
    sigprocmask(SIG_SETMASK, &old_set, NULL);
}
```

### 3. 等待信号

#### sigsuspend()原子操作
```c
void wait_for_signal_example() {
    sigset_t mask, oldmask, waitmask;
    
    // 设置信号处理器
    signal(SIGUSR1, signal_handler);
    
    // 阻塞SIGUSR1
    sigemptyset(&mask);
    sigaddset(&mask, SIGUSR1);
    sigprocmask(SIG_BLOCK, &mask, &oldmask);
    
    // 设置等待掩码（不包含SIGUSR1）
    waitmask = oldmask;
    sigdelset(&waitmask, SIGUSR1);
    
    printf("等待SIGUSR1信号...\n");
    printf("在另一个终端执行: kill -USR1 %d\n", getpid());
    
    // 原子地恢复信号掩码并等待信号
    sigsuspend(&waitmask);
    
    printf("收到信号，继续执行\n");
    
    // 恢复原始信号掩码
    sigprocmask(SIG_SETMASK, &oldmask, NULL);
}
```

## 信号安全编程

### 1. 异步信号安全函数

#### 安全函数列表
```c
// 异步信号安全的函数（部分）
// write(), read(), open(), close()
// _exit(), fork(), execve()
// signal(), sigaction(), sigprocmask()
// time(), sleep()

// 不安全的函数（不能在信号处理器中使用）
// printf(), malloc(), free()
// 大部分库函数

void safe_signal_handler(int sig) {
    // 安全：使用write()而不是printf()
    const char msg[] = "收到信号\n";
    write(STDOUT_FILENO, msg, sizeof(msg) - 1);
    
    // 不安全：不要在信号处理器中使用
    // printf("收到信号 %d\n", sig);
    // malloc(100);
}
```

### 2. 自管道技巧

#### 将异步信号转换为同步处理
```c
int signal_pipe[2];

void pipe_signal_handler(int sig) {
    char byte = sig;
    write(signal_pipe[1], &byte, 1);
}

void self_pipe_example() {
    // 创建管道
    if (pipe(signal_pipe) == -1) {
        perror("pipe");
        exit(1);
    }
    
    // 设置信号处理器
    signal(SIGINT, pipe_signal_handler);
    signal(SIGUSR1, pipe_signal_handler);
    
    // 主循环
    fd_set readfds;
    while (1) {
        FD_ZERO(&readfds);
        FD_SET(signal_pipe[0], &readfds);
        
        printf("等待信号或其他事件...\n");
        
        if (select(signal_pipe[0] + 1, &readfds, NULL, NULL, NULL) > 0) {
            if (FD_ISSET(signal_pipe[0], &readfds)) {
                char sig;
                if (read(signal_pipe[0], &sig, 1) == 1) {
                    printf("通过管道收到信号: %d\n", sig);
                    
                    // 在这里可以安全地处理信号
                    if (sig == SIGINT) {
                        printf("准备退出程序\n");
                        break;
                    }
                }
            }
        }
    }
    
    close(signal_pipe[0]);
    close(signal_pipe[1]);
}
```

## 实际应用场景

### 1. 优雅关闭服务

#### 服务器优雅关闭示例
```c
volatile int shutdown_requested = 0;

void shutdown_handler(int sig) {
    shutdown_requested = 1;
}

void graceful_server_shutdown() {
    // 注册关闭信号处理器
    signal(SIGTERM, shutdown_handler);
    signal(SIGINT, shutdown_handler);
    
    printf("服务器启动，PID: %d\n", getpid());
    
    // 服务器主循环
    while (!shutdown_requested) {
        // 处理客户端请求
        printf("处理请求中...\n");
        sleep(1);
    }
    
    printf("收到关闭信号，开始优雅关闭...\n");
    
    // 清理资源
    printf("关闭网络连接...\n");
    printf("保存数据...\n");
    printf("释放资源...\n");
    
    printf("服务器已安全关闭\n");
}
```

### 2. 子进程管理

#### 避免僵尸进程
```c
void sigchld_handler(int sig) {
    pid_t pid;
    int status;
    
    // 回收所有已终止的子进程
    while ((pid = waitpid(-1, &status, WNOHANG)) > 0) {
        printf("子进程 %d 已终止，状态: %d\n", pid, status);
    }
}

void child_process_management() {
    // 设置SIGCHLD处理器
    signal(SIGCHLD, sigchld_handler);
    
    // 创建多个子进程
    for (int i = 0; i < 5; i++) {
        pid_t pid = fork();
        
        if (pid == 0) {
            // 子进程
            printf("子进程 %d 开始工作\n", getpid());
            sleep(i + 1); // 不同的工作时间
            printf("子进程 %d 完成工作\n", getpid());
            exit(i);
        } else if (pid > 0) {
            printf("创建子进程 %d\n", pid);
        } else {
            perror("fork");
        }
    }
    
    // 父进程继续工作
    printf("父进程继续工作...\n");
    sleep(10);
    
    printf("父进程结束\n");
}
```

## 面试要点总结

1. **信号基础**：理解信号的概念、分类和特点
2. **信号处理**：掌握signal()和sigaction()的使用
3. **信号发送**：了解kill()、sigqueue()等发送方式
4. **信号掩码**：理解信号阻塞和sigprocmask()的使用
5. **安全编程**：掌握异步信号安全编程的要点
6. **实际应用**：了解信号在系统编程中的常见应用场景
7. **调试技巧**：能够调试和解决信号相关的问题
