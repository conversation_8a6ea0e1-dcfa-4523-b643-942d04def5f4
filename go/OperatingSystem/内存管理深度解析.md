# 内存管理深度解析

## 内存管理基础概念

### 1. 物理内存与虚拟内存

#### 物理内存（Physical Memory）
- **定义**：实际的RAM硬件存储空间
- **特点**：有限、昂贵、直接访问速度快
- **管理**：由操作系统内核直接管理

#### 虚拟内存（Virtual Memory）
- **定义**：操作系统提供给进程的抽象内存空间
- **优势**：
  - 进程隔离：每个进程有独立的地址空间
  - 内存保护：防止进程间相互干扰
  - 内存扩展：可以使用磁盘作为内存扩展

### 2. 地址转换机制

#### 虚拟地址到物理地址转换
```
虚拟地址 → MMU(内存管理单元) → 物理地址
```

#### 页表机制
```
虚拟地址结构：
[页号(Page Number)] [页内偏移(Page Offset)]

页表项结构：
[物理页框号] [存在位] [读写位] [用户位] [脏位] [访问位]
```

## 分页内存管理

### 1. 分页机制原理

#### 基本概念
- **页(Page)**：虚拟内存的固定大小块（通常4KB）
- **页框(Page Frame)**：物理内存的固定大小块
- **页表(Page Table)**：虚拟页到物理页框的映射表

#### 多级页表
```
64位系统的四级页表：
PGD(Page Global Directory) → PUD(Page Upper Directory) 
→ PMD(Page Middle Directory) → PTE(Page Table Entry)
```

### 2. 页面置换算法

#### FIFO（先进先出）
```python
class FIFOPageReplacement:
    def __init__(self, frame_count):
        self.frames = []
        self.frame_count = frame_count
    
    def access_page(self, page):
        if page in self.frames:
            return False  # 页面命中
        
        if len(self.frames) < self.frame_count:
            self.frames.append(page)
        else:
            # 替换最早进入的页面
            self.frames.pop(0)
            self.frames.append(page)
        
        return True  # 页面缺失
```

#### LRU（最近最少使用）
```python
class LRUPageReplacement:
    def __init__(self, frame_count):
        self.frames = {}  # {page: access_time}
        self.frame_count = frame_count
        self.time = 0
    
    def access_page(self, page):
        self.time += 1
        
        if page in self.frames:
            self.frames[page] = self.time
            return False  # 页面命中
        
        if len(self.frames) < self.frame_count:
            self.frames[page] = self.time
        else:
            # 找到最久未使用的页面
            lru_page = min(self.frames, key=self.frames.get)
            del self.frames[lru_page]
            self.frames[page] = self.time
        
        return True  # 页面缺失
```

#### Clock算法（近似LRU）
```c
typedef struct {
    int page_number;
    int reference_bit;
} PageFrame;

int clock_replacement(PageFrame frames[], int frame_count, int new_page) {
    static int clock_hand = 0;
    
    while (1) {
        if (frames[clock_hand].reference_bit == 0) {
            // 找到可替换的页面
            int old_page = frames[clock_hand].page_number;
            frames[clock_hand].page_number = new_page;
            frames[clock_hand].reference_bit = 1;
            clock_hand = (clock_hand + 1) % frame_count;
            return old_page;
        } else {
            // 给第二次机会
            frames[clock_hand].reference_bit = 0;
            clock_hand = (clock_hand + 1) % frame_count;
        }
    }
}
```

### 3. 工作集模型

#### 工作集定义
```
W(t, Δ) = {页面 | 页面在时间间隔[t-Δ, t]内被访问}
```

#### 工作集算法实现
```python
class WorkingSetModel:
    def __init__(self, window_size):
        self.window_size = window_size
        self.access_history = []
        self.working_set = set()
    
    def access_page(self, page, current_time):
        # 添加新的访问记录
        self.access_history.append((page, current_time))
        
        # 移除窗口外的访问记录
        cutoff_time = current_time - self.window_size
        self.access_history = [
            (p, t) for p, t in self.access_history 
            if t > cutoff_time
        ]
        
        # 更新工作集
        self.working_set = {p for p, t in self.access_history}
        
        return self.working_set
```

## 内存分配算法

### 1. 连续内存分配

#### 首次适应算法（First Fit）
```c
typedef struct MemoryBlock {
    size_t size;
    int is_free;
    struct MemoryBlock* next;
} MemoryBlock;

MemoryBlock* first_fit_allocate(MemoryBlock* head, size_t size) {
    MemoryBlock* current = head;
    
    while (current != NULL) {
        if (current->is_free && current->size >= size) {
            // 找到第一个足够大的空闲块
            if (current->size > size) {
                // 分割内存块
                MemoryBlock* new_block = (MemoryBlock*)malloc(sizeof(MemoryBlock));
                new_block->size = current->size - size;
                new_block->is_free = 1;
                new_block->next = current->next;
                
                current->size = size;
                current->next = new_block;
            }
            
            current->is_free = 0;
            return current;
        }
        current = current->next;
    }
    
    return NULL; // 分配失败
}
```

#### 最佳适应算法（Best Fit）
```c
MemoryBlock* best_fit_allocate(MemoryBlock* head, size_t size) {
    MemoryBlock* current = head;
    MemoryBlock* best_block = NULL;
    size_t min_waste = SIZE_MAX;
    
    while (current != NULL) {
        if (current->is_free && current->size >= size) {
            size_t waste = current->size - size;
            if (waste < min_waste) {
                min_waste = waste;
                best_block = current;
            }
        }
        current = current->next;
    }
    
    if (best_block != NULL) {
        // 分配找到的最佳块
        allocate_block(best_block, size);
    }
    
    return best_block;
}
```

### 2. 伙伴系统算法

#### 伙伴系统实现
```c
#define MAX_ORDER 10

typedef struct BuddySystem {
    struct list_head free_list[MAX_ORDER + 1];
    unsigned long *bitmap;
} BuddySystem;

void* buddy_alloc(BuddySystem* buddy, int order) {
    int current_order = order;
    
    // 寻找合适大小的空闲块
    while (current_order <= MAX_ORDER) {
        if (!list_empty(&buddy->free_list[current_order])) {
            // 找到空闲块
            struct page* page = list_first_entry(
                &buddy->free_list[current_order], 
                struct page, lru
            );
            list_del(&page->lru);
            
            // 如果块太大，需要分割
            while (current_order > order) {
                current_order--;
                struct page* buddy_page = page + (1 << current_order);
                list_add(&buddy_page->lru, &buddy->free_list[current_order]);
            }
            
            return page;
        }
        current_order++;
    }
    
    return NULL; // 分配失败
}

void buddy_free(BuddySystem* buddy, void* ptr, int order) {
    struct page* page = (struct page*)ptr;
    
    // 尝试与伙伴合并
    while (order < MAX_ORDER) {
        unsigned long buddy_pfn = page_to_pfn(page) ^ (1 << order);
        struct page* buddy_page = pfn_to_page(buddy_pfn);
        
        if (!page_is_buddy(buddy_page, order)) {
            break; // 伙伴不空闲，无法合并
        }
        
        // 合并伙伴
        list_del(&buddy_page->lru);
        if (buddy_page < page) {
            page = buddy_page;
        }
        order++;
    }
    
    // 将合并后的块加入空闲列表
    list_add(&page->lru, &buddy->free_list[order]);
}
```

## 内存回收机制

### 1. 垃圾回收算法

#### 标记-清除算法
```python
class MarkAndSweepGC:
    def __init__(self):
        self.objects = []
        self.roots = []
    
    def mark_phase(self):
        # 标记阶段：从根对象开始标记所有可达对象
        marked = set()
        stack = list(self.roots)
        
        while stack:
            obj = stack.pop()
            if obj not in marked:
                marked.add(obj)
                # 添加对象引用的其他对象
                stack.extend(obj.references)
        
        return marked
    
    def sweep_phase(self, marked):
        # 清除阶段：回收未标记的对象
        alive_objects = []
        for obj in self.objects:
            if obj in marked:
                alive_objects.append(obj)
            else:
                obj.finalize()  # 释放对象资源
        
        self.objects = alive_objects
    
    def collect(self):
        marked = self.mark_phase()
        self.sweep_phase(marked)
```

#### 复制算法
```python
class CopyingGC:
    def __init__(self, heap_size):
        self.heap_size = heap_size
        self.from_space = [None] * (heap_size // 2)
        self.to_space = [None] * (heap_size // 2)
        self.allocation_pointer = 0
    
    def collect(self):
        # 复制所有活跃对象到to_space
        new_allocation_pointer = 0
        
        for root in self.roots:
            if root is not None:
                new_address = self.copy_object(root, new_allocation_pointer)
                new_allocation_pointer += 1
        
        # 交换from_space和to_space
        self.from_space, self.to_space = self.to_space, self.from_space
        self.allocation_pointer = new_allocation_pointer
        
        # 清空新的to_space
        self.to_space = [None] * (self.heap_size // 2)
    
    def copy_object(self, obj, new_address):
        if obj.forwarding_address is not None:
            return obj.forwarding_address
        
        # 复制对象到新位置
        self.to_space[new_address] = obj.copy()
        obj.forwarding_address = new_address
        
        # 递归复制引用的对象
        for ref in obj.references:
            if ref is not None:
                new_ref_address = self.copy_object(ref, new_address + 1)
                new_address += 1
        
        return new_address
```

### 2. 内存压缩

#### 内存碎片整理
```c
void memory_compaction(MemoryManager* mm) {
    void* compact_ptr = mm->heap_start;
    void* scan_ptr = mm->heap_start;
    
    while (scan_ptr < mm->heap_end) {
        Object* obj = (Object*)scan_ptr;
        
        if (obj->is_marked) {
            if (compact_ptr != scan_ptr) {
                // 移动对象到压缩位置
                memmove(compact_ptr, scan_ptr, obj->size);
                
                // 更新所有指向该对象的引用
                update_references(scan_ptr, compact_ptr);
            }
            
            compact_ptr += obj->size;
        }
        
        scan_ptr += obj->size;
    }
    
    // 更新堆的结束位置
    mm->heap_end = compact_ptr;
}
```

## 内存保护机制

### 1. 段保护
```c
// 段描述符结构
typedef struct {
    uint32_t limit_low : 16;
    uint32_t base_low : 24;
    uint32_t type : 4;
    uint32_t s : 1;        // 系统段标志
    uint32_t dpl : 2;      // 描述符特权级
    uint32_t p : 1;        // 存在位
    uint32_t limit_high : 4;
    uint32_t avl : 1;      // 可用位
    uint32_t l : 1;        // 64位代码段
    uint32_t db : 1;       // 默认操作大小
    uint32_t g : 1;        // 粒度位
    uint32_t base_high : 8;
} SegmentDescriptor;
```

### 2. 页保护
```c
// 页表项保护位
#define PAGE_PRESENT    0x001
#define PAGE_WRITABLE   0x002
#define PAGE_USER       0x004
#define PAGE_ACCESSED   0x020
#define PAGE_DIRTY      0x040
#define PAGE_EXECUTE    0x080

int check_page_permission(uint32_t pte, int access_type, int user_mode) {
    if (!(pte & PAGE_PRESENT)) {
        return PAGE_FAULT_NOT_PRESENT;
    }
    
    if (user_mode && !(pte & PAGE_USER)) {
        return PAGE_FAULT_PROTECTION;
    }
    
    if ((access_type == WRITE) && !(pte & PAGE_WRITABLE)) {
        return PAGE_FAULT_PROTECTION;
    }
    
    if ((access_type == EXECUTE) && !(pte & PAGE_EXECUTE)) {
        return PAGE_FAULT_PROTECTION;
    }
    
    return PAGE_ACCESS_OK;
}
```

## 常见面试问题

### 1. 虚拟内存的优势是什么？
- **进程隔离**：每个进程有独立的地址空间
- **内存保护**：防止进程间相互干扰
- **内存扩展**：可以使用磁盘作为内存扩展
- **内存共享**：多个进程可以共享同一物理内存

### 2. 页面置换算法的比较
| 算法 | 时间复杂度 | 空间复杂度 | 优点 | 缺点 |
|------|------------|------------|------|------|
| FIFO | O(1) | O(n) | 简单实现 | 可能出现Belady异常 |
| LRU | O(1) | O(n) | 性能好 | 实现复杂，开销大 |
| Clock | O(n) | O(n) | 近似LRU，开销小 | 性能略差于LRU |

### 3. 内存碎片问题
- **内部碎片**：分配的内存块内部未使用的空间
- **外部碎片**：空闲内存块太小无法满足分配请求
- **解决方案**：伙伴系统、内存池、垃圾回收

### 4. 内存泄漏检测
```c
// 简单的内存泄漏检测
typedef struct {
    void* ptr;
    size_t size;
    const char* file;
    int line;
} AllocInfo;

static AllocInfo alloc_table[MAX_ALLOCS];
static int alloc_count = 0;

void* debug_malloc(size_t size, const char* file, int line) {
    void* ptr = malloc(size);
    if (ptr && alloc_count < MAX_ALLOCS) {
        alloc_table[alloc_count].ptr = ptr;
        alloc_table[alloc_count].size = size;
        alloc_table[alloc_count].file = file;
        alloc_table[alloc_count].line = line;
        alloc_count++;
    }
    return ptr;
}

void debug_free(void* ptr) {
    for (int i = 0; i < alloc_count; i++) {
        if (alloc_table[i].ptr == ptr) {
            // 移除分配记录
            memmove(&alloc_table[i], &alloc_table[i+1], 
                   (alloc_count - i - 1) * sizeof(AllocInfo));
            alloc_count--;
            break;
        }
    }
    free(ptr);
}

void check_memory_leaks() {
    printf("Memory leaks detected:\n");
    for (int i = 0; i < alloc_count; i++) {
        printf("Leak: %zu bytes at %s:%d\n", 
               alloc_table[i].size, 
               alloc_table[i].file, 
               alloc_table[i].line);
    }
}
```

## 面试要点总结

1. **基本概念**：理解物理内存、虚拟内存、地址转换机制
2. **分页机制**：掌握页表结构、多级页表、页面置换算法
3. **内存分配**：了解各种内存分配算法的优缺点
4. **内存回收**：理解垃圾回收算法和内存压缩机制
5. **内存保护**：掌握段保护和页保护机制
6. **性能优化**：了解内存访问优化和缓存机制
7. **问题诊断**：能够分析和解决内存相关问题
