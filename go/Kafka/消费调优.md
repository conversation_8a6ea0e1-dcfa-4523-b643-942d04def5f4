消费调优 Kafka 消费者的性能可以通过以下几个方面进行优化：

1. **并行消费**：增加消费者实例，利用多个消费者并行处理消息。确保消费者组的数量与分区数相匹配。

2. **批量处理**：使用批量消费配置，例如 `max.poll.records`，设置一次从 Kafka 拉取多条消息，减少网络往返次数。

3. **合理配置 `fetch` 参数**：调整 `fetch.min.bytes` 和 `fetch.max.wait.ms`，优化消息拉取的效率。

4. **调整 `session.timeout.ms` 和 `max.poll.interval.ms`**：确保消费者能够在适当的时间内处理消息，防止因超时导致的重新平衡。

5. **高效的消息处理**：优化消息处理逻辑，确保消费者在 `poll()` 后尽快处理消息并提交位移。

6. **监控和调试**：使用 Kafka 监控工具（如 Confluent Control Center、Prometheus + Grafana）监控消费者的性能，发现瓶颈并进行调整。

7. **调整 `auto.offset.reset`**：设置为 `latest` 或 `earliest`，根据业务需求选择适当的偏移重置策略。 

通过这些调优策略，可以提高 Kafka 消费者的性能和稳定性。