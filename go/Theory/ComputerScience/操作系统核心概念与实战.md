# 操作系统核心概念与实战

## 1. 进程与线程深度解析

### 1.1 进程控制块（PCB）结构

```c
struct task_struct {
    int pid;                    // 进程ID
    int state;                  // 进程状态
    struct mm_struct *mm;       // 内存管理结构
    struct files_struct *files; // 文件描述符表
    struct signal_struct *signal; // 信号处理
    struct task_struct *parent;   // 父进程指针
    struct list_head children;    // 子进程链表
    struct task_struct *next_task, *prev_task; // 进程链表
    
    // 调度相关
    int priority;               // 优先级
    int nice;                   // nice值
    unsigned long policy;       // 调度策略
    
    // 时间统计
    clock_t utime, stime;       // 用户态和内核态时间
    clock_t start_time;         // 进程启动时间
};
```

### 1.2 进程状态转换实战演示

```go
package main

import (
    "fmt"
    "os"
    "os/exec"
    "syscall"
    "time"
)

// 演示进程状态转换
func demonstrateProcessStates() {
    fmt.Println("=== 进程状态转换演示 ===")
    
    // 1. 创建子进程 (NEW -> READY)
    cmd := exec.Command("sleep", "5")
    cmd.Start()
    pid := cmd.Process.Pid
    fmt.Printf("创建子进程，PID: %d (NEW -> READY)\n", pid)
    
    // 2. 进程运行 (READY -> RUNNING)
    fmt.Printf("进程 %d 开始运行 (READY -> RUNNING)\n", pid)
    
    // 3. 检查进程状态
    go func() {
        for i := 0; i < 6; i++ {
            if process, err := os.FindProcess(pid); err == nil {
                fmt.Printf("进程 %d 状态检查 - 时间: %ds\n", pid, i)
                time.Sleep(time.Second)
            }
        }
    }()
    
    // 4. 等待进程结束 (RUNNING -> TERMINATED)
    cmd.Wait()
    fmt.Printf("进程 %d 已终止 (RUNNING -> TERMINATED)\n", pid)
}

// 演示进程间通信 - 管道
func demonstrateIPC() {
    fmt.Println("\n=== 进程间通信演示 ===")
    
    // 创建管道
    cmd1 := exec.Command("echo", "Hello from process 1")
    cmd2 := exec.Command("grep", "Hello")
    
    // 连接管道
    pipe, err := cmd1.StdoutPipe()
    if err != nil {
        panic(err)
    }
    cmd2.Stdin = pipe
    
    // 启动进程
    cmd2.Start()
    cmd1.Run()
    pipe.Close()
    
    output, err := cmd2.Output()
    if err != nil {
        panic(err)
    }
    
    fmt.Printf("管道通信结果: %s", output)
}
```

### 1.3 线程同步机制实现

```go
import (
    "sync"
    "sync/atomic"
    "time"
)

// 信号量实现
type Semaphore struct {
    permits int32
    cond    *sync.Cond
    mutex   sync.Mutex
}

func NewSemaphore(permits int32) *Semaphore {
    s := &Semaphore{permits: permits}
    s.cond = sync.NewCond(&s.mutex)
    return s
}

func (s *Semaphore) Acquire() {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    for s.permits <= 0 {
        s.cond.Wait()
    }
    s.permits--
}

func (s *Semaphore) Release() {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    s.permits++
    s.cond.Signal()
}

// 哲学家就餐问题解决方案
func philosophersDining() {
    const numPhilosophers = 5
    forks := make([]*sync.Mutex, numPhilosophers)
    for i := range forks {
        forks[i] = &sync.Mutex{}
    }
    
    philosopher := func(id int) {
        left := id
        right := (id + 1) % numPhilosophers
        
        // 避免死锁：按序获取叉子
        if left > right {
            left, right = right, left
        }
        
        for i := 0; i < 3; i++ {
            // 思考
            fmt.Printf("哲学家 %d 正在思考\n", id)
            time.Sleep(time.Millisecond * 100)
            
            // 获取叉子
            forks[left].Lock()
            forks[right].Lock()
            
            // 就餐
            fmt.Printf("哲学家 %d 正在就餐\n", id)
            time.Sleep(time.Millisecond * 200)
            
            // 放下叉子
            forks[right].Unlock()
            forks[left].Unlock()
            
            fmt.Printf("哲学家 %d 就餐完毕\n", id)
        }
    }
    
    var wg sync.WaitGroup
    for i := 0; i < numPhilosophers; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            philosopher(id)
        }(i)
    }
    wg.Wait()
}
```

## 2. 内存管理深度解析

### 2.1 虚拟内存管理

```go
// 模拟页表结构
type PageTableEntry struct {
    Present    bool   // 页面是否在内存中
    Writable   bool   // 是否可写
    User       bool   // 用户态是否可访问
    Accessed   bool   // 是否被访问过
    Dirty      bool   // 是否被修改过
    PhysAddr   uint64 // 物理地址
}

type PageTable struct {
    entries []PageTableEntry
    size    int
}

func NewPageTable(size int) *PageTable {
    return &PageTable{
        entries: make([]PageTableEntry, size),
        size:    size,
    }
}

// 地址转换
func (pt *PageTable) TranslateAddress(virtualAddr uint64) (uint64, error) {
    pageNum := virtualAddr >> 12  // 假设页大小为4KB
    offset := virtualAddr & 0xFFF
    
    if int(pageNum) >= pt.size {
        return 0, fmt.Errorf("页面越界")
    }
    
    entry := pt.entries[pageNum]
    if !entry.Present {
        return 0, fmt.Errorf("页面不在内存中")
    }
    
    physAddr := entry.PhysAddr + offset
    entry.Accessed = true
    pt.entries[pageNum] = entry
    
    return physAddr, nil
}

// LRU页面置换算法
type LRUPageReplacer struct {
    capacity int
    pages    map[int]*Node
    head     *Node
    tail     *Node
}

type Node struct {
    key  int
    prev *Node
    next *Node
}

func NewLRUPageReplacer(capacity int) *LRUPageReplacer {
    lru := &LRUPageReplacer{
        capacity: capacity,
        pages:    make(map[int]*Node),
    }
    
    // 创建哨兵节点
    lru.head = &Node{}
    lru.tail = &Node{}
    lru.head.next = lru.tail
    lru.tail.prev = lru.head
    
    return lru
}

func (lru *LRUPageReplacer) Access(pageNum int) int {
    if node, exists := lru.pages[pageNum]; exists {
        // 页面命中，移到头部
        lru.moveToHead(node)
        return -1 // 无需置换
    }
    
    // 页面缺失
    newNode := &Node{key: pageNum}
    
    if len(lru.pages) >= lru.capacity {
        // 需要置换，移除尾部页面
        tail := lru.removeTail()
        delete(lru.pages, tail.key)
        
        // 添加新页面到头部
        lru.addToHead(newNode)
        lru.pages[pageNum] = newNode
        
        return tail.key // 返回被置换的页面
    }
    
    // 容量未满，直接添加
    lru.addToHead(newNode)
    lru.pages[pageNum] = newNode
    return -1
}

func (lru *LRUPageReplacer) moveToHead(node *Node) {
    lru.removeNode(node)
    lru.addToHead(node)
}

func (lru *LRUPageReplacer) removeNode(node *Node) {
    node.prev.next = node.next
    node.next.prev = node.prev
}

func (lru *LRUPageReplacer) addToHead(node *Node) {
    node.prev = lru.head
    node.next = lru.head.next
    lru.head.next.prev = node
    lru.head.next = node
}

func (lru *LRUPageReplacer) removeTail() *Node {
    lastNode := lru.tail.prev
    lru.removeNode(lastNode)
    return lastNode
}
```

### 2.2 内存分配算法

```go
// 伙伴系统内存分配器
type BuddyAllocator struct {
    memory    []byte
    freeList  [][]int  // 每个级别的空闲块链表
    maxOrder  int
    blockSize int
}

func NewBuddyAllocator(totalSize, minBlockSize int) *BuddyAllocator {
    maxOrder := 0
    for (minBlockSize << maxOrder) < totalSize {
        maxOrder++
    }
    
    ba := &BuddyAllocator{
        memory:    make([]byte, totalSize),
        freeList:  make([][]int, maxOrder+1),
        maxOrder:  maxOrder,
        blockSize: minBlockSize,
    }
    
    // 初始化：整个内存作为最大的空闲块
    ba.freeList[maxOrder] = []int{0}
    
    return ba
}

func (ba *BuddyAllocator) Allocate(size int) (int, error) {
    // 计算需要的阶数
    order := 0
    for (ba.blockSize << order) < size {
        order++
    }
    
    if order > ba.maxOrder {
        return -1, fmt.Errorf("请求的内存太大")
    }
    
    // 寻找合适的空闲块
    for currentOrder := order; currentOrder <= ba.maxOrder; currentOrder++ {
        if len(ba.freeList[currentOrder]) > 0 {
            // 找到空闲块
            blockIndex := ba.freeList[currentOrder][0]
            ba.freeList[currentOrder] = ba.freeList[currentOrder][1:]
            
            // 分裂块直到达到所需大小
            for currentOrder > order {
                currentOrder--
                buddyIndex := blockIndex ^ (1 << currentOrder)
                ba.freeList[currentOrder] = append(ba.freeList[currentOrder], buddyIndex)
            }
            
            return blockIndex, nil
        }
    }
    
    return -1, fmt.Errorf("内存不足")
}

func (ba *BuddyAllocator) Deallocate(blockIndex, order int) {
    // 尝试与伙伴合并
    for order < ba.maxOrder {
        buddyIndex := blockIndex ^ (1 << order)
        
        // 检查伙伴是否空闲
        found := false
        for i, freeBlock := range ba.freeList[order] {
            if freeBlock == buddyIndex {
                // 找到空闲的伙伴，进行合并
                ba.freeList[order] = append(ba.freeList[order][:i], ba.freeList[order][i+1:]...)
                if blockIndex > buddyIndex {
                    blockIndex = buddyIndex
                }
                order++
                found = true
                break
            }
        }
        
        if !found {
            break
        }
    }
    
    // 将合并后的块加入空闲链表
    ba.freeList[order] = append(ba.freeList[order], blockIndex)
}
```

## 3. I/O系统与设备管理

### 3.1 I/O多路复用实现

```go
import (
    "net"
    "syscall"
    "unsafe"
)

// Epoll封装
type Epoll struct {
    fd int
}

func NewEpoll() (*Epoll, error) {
    fd, err := syscall.EpollCreate1(0)
    if err != nil {
        return nil, err
    }
    return &Epoll{fd: fd}, nil
}

func (e *Epoll) Add(fd int, events uint32) error {
    event := syscall.EpollEvent{
        Events: events,
        Fd:     int32(fd),
    }
    return syscall.EpollCtl(e.fd, syscall.EPOLL_CTL_ADD, fd, &event)
}

func (e *Epoll) Wait(maxEvents int, timeout int) ([]syscall.EpollEvent, error) {
    events := make([]syscall.EpollEvent, maxEvents)
    n, err := syscall.EpollWait(e.fd, events, timeout)
    if err != nil {
        return nil, err
    }
    return events[:n], nil
}

// 高性能网络服务器示例
func highPerformanceServer() {
    // 创建监听socket
    listener, err := net.Listen("tcp", ":8080")
    if err != nil {
        panic(err)
    }
    defer listener.Close()
    
    // 获取文件描述符
    file, err := listener.(*net.TCPListener).File()
    if err != nil {
        panic(err)
    }
    listenerFd := int(file.Fd())
    
    // 创建epoll实例
    epoll, err := NewEpoll()
    if err != nil {
        panic(err)
    }
    
    // 添加监听socket到epoll
    err = epoll.Add(listenerFd, syscall.EPOLLIN)
    if err != nil {
        panic(err)
    }
    
    fmt.Println("服务器启动，监听端口8080")
    
    for {
        events, err := epoll.Wait(10, -1)
        if err != nil {
            continue
        }
        
        for _, event := range events {
            if int(event.Fd) == listenerFd {
                // 新连接
                conn, err := listener.Accept()
                if err != nil {
                    continue
                }
                
                // 设置非阻塞
                file, _ := conn.(*net.TCPConn).File()
                connFd := int(file.Fd())
                syscall.SetNonblock(connFd, true)
                
                // 添加到epoll
                epoll.Add(connFd, syscall.EPOLLIN|syscall.EPOLLET)
                
                fmt.Printf("新连接: fd=%d\n", connFd)
            } else {
                // 数据可读
                fd := int(event.Fd)
                buffer := make([]byte, 1024)
                n, err := syscall.Read(fd, buffer)
                if err != nil || n == 0 {
                    syscall.Close(fd)
                    continue
                }
                
                // 回显数据
                syscall.Write(fd, buffer[:n])
            }
        }
    }
}
```

## 4. 文件系统深度解析

### 4.1 inode结构与文件操作

```go
// 模拟inode结构
type Inode struct {
    Number      uint64    // inode号
    Mode        uint32    // 文件类型和权限
    Size        int64     // 文件大小
    Blocks      int64     // 占用的块数
    AccessTime  time.Time // 访问时间
    ModifyTime  time.Time // 修改时间
    ChangeTime  time.Time // 状态改变时间
    DirectBlocks [12]uint64 // 直接块指针
    IndirectBlock uint64    // 间接块指针
    DoubleIndirectBlock uint64 // 二级间接块指针
    TripleIndirectBlock uint64 // 三级间接块指针
}

// 文件系统操作
type FileSystem struct {
    inodes      map[uint64]*Inode
    blocks      [][]byte
    freeInodes  []uint64
    freeBlocks  []uint64
    blockSize   int
}

func NewFileSystem(totalBlocks, blockSize int) *FileSystem {
    fs := &FileSystem{
        inodes:     make(map[uint64]*Inode),
        blocks:     make([][]byte, totalBlocks),
        freeBlocks: make([]uint64, 0, totalBlocks),
        blockSize:  blockSize,
    }
    
    // 初始化空闲块列表
    for i := 0; i < totalBlocks; i++ {
        fs.blocks[i] = make([]byte, blockSize)
        fs.freeBlocks = append(fs.freeBlocks, uint64(i))
    }
    
    return fs
}

func (fs *FileSystem) AllocateInode() uint64 {
    if len(fs.freeInodes) > 0 {
        inodeNum := fs.freeInodes[0]
        fs.freeInodes = fs.freeInodes[1:]
        return inodeNum
    }
    
    // 简化：使用map长度作为新inode号
    return uint64(len(fs.inodes))
}

func (fs *FileSystem) AllocateBlock() uint64 {
    if len(fs.freeBlocks) == 0 {
        return 0 // 无可用块
    }
    
    blockNum := fs.freeBlocks[0]
    fs.freeBlocks = fs.freeBlocks[1:]
    return blockNum
}

func (fs *FileSystem) CreateFile(name string, size int64) uint64 {
    inodeNum := fs.AllocateInode()
    inode := &Inode{
        Number:     inodeNum,
        Mode:       0644, // 普通文件权限
        Size:       size,
        AccessTime: time.Now(),
        ModifyTime: time.Now(),
        ChangeTime: time.Now(),
    }
    
    // 分配数据块
    blocksNeeded := (size + int64(fs.blockSize) - 1) / int64(fs.blockSize)
    for i := int64(0); i < blocksNeeded && i < 12; i++ {
        blockNum := fs.AllocateBlock()
        if blockNum == 0 {
            break
        }
        inode.DirectBlocks[i] = blockNum
        inode.Blocks++
    }
    
    fs.inodes[inodeNum] = inode
    return inodeNum
}
```

## 5. 系统调用与中断处理

### 5.1 系统调用实现机制

```go
// 模拟系统调用表
type SystemCall func(args ...interface{}) (interface{}, error)

var syscallTable = map[int]SystemCall{
    1: sysRead,
    2: sysWrite,
    3: sysOpen,
    4: sysClose,
}

func sysRead(args ...interface{}) (interface{}, error) {
    if len(args) < 3 {
        return nil, fmt.Errorf("参数不足")
    }
    
    fd := args[0].(int)
    buffer := args[1].([]byte)
    count := args[2].(int)
    
    fmt.Printf("系统调用 read: fd=%d, count=%d\n", fd, count)
    
    // 模拟读取操作
    data := fmt.Sprintf("数据来自文件描述符 %d", fd)
    copy(buffer, data)
    
    return len(data), nil
}

func sysWrite(args ...interface{}) (interface{}, error) {
    fd := args[0].(int)
    buffer := args[1].([]byte)
    count := args[2].(int)
    
    fmt.Printf("系统调用 write: fd=%d, data=%s\n", fd, string(buffer[:count]))
    
    return count, nil
}

// 中断处理程序
type InterruptHandler func(interruptNum int)

var interruptTable = map[int]InterruptHandler{
    0:  divideByZeroHandler,
    14: pageFaultHandler,
    32: timerInterruptHandler,
}

func divideByZeroHandler(interruptNum int) {
    fmt.Printf("处理除零异常，中断号: %d\n", interruptNum)
    // 异常处理逻辑
}

func pageFaultHandler(interruptNum int) {
    fmt.Printf("处理页面错误，中断号: %d\n", interruptNum)
    // 页面错误处理逻辑
}

func timerInterruptHandler(interruptNum int) {
    fmt.Printf("处理时钟中断，中断号: %d\n", interruptNum)
    // 进程调度逻辑
}
```

## 6. 面试重点问题解析

### Q1: 进程和线程的区别？
- **资源分配**：进程拥有独立地址空间，线程共享进程地址空间
- **创建开销**：线程创建开销小于进程
- **通信方式**：进程需要IPC，线程可直接共享内存
- **安全性**：进程间相互独立，线程崩溃可能影响整个进程

### Q2: 虚拟内存的作用？
1. **地址空间扩展**：提供比物理内存更大的地址空间
2. **内存保护**：进程间内存隔离
3. **内存共享**：多进程共享代码段
4. **按需加载**：只加载需要的页面

### Q3: 死锁的四个必要条件？
1. **互斥条件**：资源不能被多个进程同时使用
2. **占有和等待**：进程持有资源的同时等待其他资源
3. **不可抢占**：资源不能被强制释放
4. **循环等待**：存在进程资源的循环等待链

### Q4: I/O多路复用的优势？
- **高并发**：单线程处理多个连接
- **低开销**：避免线程切换成本
- **可扩展**：支持大量并发连接
- **事件驱动**：响应式编程模型

这些操作系统概念是系统编程和性能优化的基础，深入理解有助于构建高性能系统。
