# 分布式系统设计原则

## 1. 分布式系统基础

### 1.1 分布式系统定义
分布式系统是由多个独立的计算机组成，通过网络连接，协同工作来完成共同任务的系统。

**核心特征**：
- **分布性**：组件分布在不同的物理位置
- **并发性**：多个组件同时执行
- **缺乏全局时钟**：无法精确同步所有节点
- **独立故障**：部分组件故障不影响整体

### 1.2 分布式系统挑战
```go
// 分布式系统面临的主要挑战
type DistributedChallenges struct {
    NetworkPartition bool   // 网络分区
    NodeFailure     bool   // 节点故障
    MessageLoss     bool   // 消息丢失
    MessageDelay    bool   // 消息延迟
    ClockSkew       bool   // 时钟偏差
    Concurrency     bool   // 并发控制
}

// 故障类型
const (
    FAIL_STOP    = "fail-stop"    // 停止故障
    FAIL_CRASH   = "fail-crash"   // 崩溃故障
    FAIL_OMISSION = "fail-omission" // 遗漏故障
    FAIL_BYZANTINE = "fail-byzantine" // 拜占庭故障
)
```

## 2. CAP定理

### 2.1 CAP定理内容
```go
// CAP定理：分布式系统最多只能同时保证三个特性中的两个
type CAPTheorem struct {
    Consistency   bool // 一致性：所有节点同时看到相同数据
    Availability  bool // 可用性：系统持续提供服务
    Partition     bool // 分区容错：网络分区时系统继续工作
}

// CAP选择策略
func chooseCAP(requirement string) CAPTheorem {
    switch requirement {
    case "CP": // 一致性 + 分区容错
        return CAPTheorem{
            Consistency: true,
            Availability: false,
            Partition: true,
        }
    case "AP": // 可用性 + 分区容错
        return CAPTheorem{
            Consistency: false,
            Availability: true,
            Partition: true,
        }
    case "CA": // 一致性 + 可用性（理论上，实际不存在）
        return CAPTheorem{
            Consistency: true,
            Availability: true,
            Partition: false,
        }
    default:
        return CAPTheorem{}
    }
}
```

### 2.2 一致性模型
```go
// 一致性级别
type ConsistencyLevel int

const (
    STRONG_CONSISTENCY ConsistencyLevel = iota // 强一致性
    WEAK_CONSISTENCY                          // 弱一致性
    EVENTUAL_CONSISTENCY                      // 最终一致性
    CAUSAL_CONSISTENCY                        // 因果一致性
    SESSION_CONSISTENCY                       // 会话一致性
)

// 一致性实现示例
type ConsistentStorage struct {
    data      map[string]string
    version   map[string]int
    replicas  []string
    quorum    int
}

// 强一致性读取
func (cs *ConsistentStorage) StrongRead(key string) (string, error) {
    // 需要从大多数节点读取并比较版本
    responses := make(map[string]int)
    
    for _, replica := range cs.replicas {
        value, version := cs.readFromReplica(replica, key)
        responses[value] = version
    }
    
    // 选择版本最高的值
    var latestValue string
    maxVersion := -1
    
    for value, version := range responses {
        if version > maxVersion {
            maxVersion = version
            latestValue = value
        }
    }
    
    return latestValue, nil
}

// 最终一致性写入
func (cs *ConsistentStorage) EventualWrite(key, value string) error {
    // 异步写入所有副本
    cs.version[key]++
    newVersion := cs.version[key]
    
    for _, replica := range cs.replicas {
        go cs.writeToReplica(replica, key, value, newVersion)
    }
    
    return nil
}
```

## 3. BASE理论

### 3.1 BASE vs ACID
```go
// ACID特性（传统数据库）
type ACIDProperties struct {
    Atomicity    bool // 原子性
    Consistency  bool // 一致性
    Isolation    bool // 隔离性
    Durability   bool // 持久性
}

// BASE特性（分布式系统）
type BASEProperties struct {
    BasicAvailability bool // 基本可用
    SoftState        bool // 软状态
    EventualConsistency bool // 最终一致性
}

// BASE实现示例
type BASESystem struct {
    primaryDB   Database
    secondaryDB Database
    syncQueue   chan SyncOperation
}

type SyncOperation struct {
    Operation string
    Key       string
    Value     string
    Timestamp time.Time
}

// 基本可用：即使部分节点故障，系统仍可提供服务
func (bs *BASESystem) Write(key, value string) error {
    // 先写主库
    if err := bs.primaryDB.Write(key, value); err != nil {
        // 主库失败，尝试写从库
        return bs.secondaryDB.Write(key, value)
    }
    
    // 异步同步到从库
    bs.syncQueue <- SyncOperation{
        Operation: "write",
        Key:       key,
        Value:     value,
        Timestamp: time.Now(),
    }
    
    return nil
}

// 软状态：系统状态可能在一段时间内不一致
func (bs *BASESystem) backgroundSync() {
    for op := range bs.syncQueue {
        // 异步同步操作
        time.Sleep(100 * time.Millisecond) // 模拟网络延迟
        
        switch op.Operation {
        case "write":
            bs.secondaryDB.Write(op.Key, op.Value)
        case "delete":
            bs.secondaryDB.Delete(op.Key)
        }
    }
}
```

## 4. 分布式一致性算法

### 4.1 Raft算法
```go
type RaftState int

const (
    FOLLOWER RaftState = iota
    CANDIDATE
    LEADER
)

type RaftNode struct {
    id           int
    state        RaftState
    currentTerm  int
    votedFor     int
    log          []LogEntry
    commitIndex  int
    lastApplied  int
    
    // Leader状态
    nextIndex    []int
    matchIndex   []int
    
    peers        []int
    electionTimeout time.Duration
    heartbeatInterval time.Duration
}

type LogEntry struct {
    Term    int
    Index   int
    Command interface{}
}

// 选举过程
func (rn *RaftNode) startElection() {
    rn.state = CANDIDATE
    rn.currentTerm++
    rn.votedFor = rn.id
    
    votes := 1 // 投票给自己
    
    for _, peer := range rn.peers {
        go func(peerID int) {
            if rn.requestVote(peerID) {
                votes++
                if votes > len(rn.peers)/2 {
                    rn.becomeLeader()
                }
            }
        }(peer)
    }
}

// 日志复制
func (rn *RaftNode) appendEntries(entry LogEntry) bool {
    if rn.state != LEADER {
        return false
    }
    
    // 添加到本地日志
    rn.log = append(rn.log, entry)
    
    // 复制到大多数节点
    successCount := 1 // 自己
    
    for _, peer := range rn.peers {
        if rn.sendAppendEntries(peer, entry) {
            successCount++
        }
    }
    
    // 如果大多数节点成功，提交日志
    if successCount > len(rn.peers)/2 {
        rn.commitIndex = len(rn.log) - 1
        return true
    }
    
    return false
}
```

### 4.2 Paxos算法
```go
type PaxosNode struct {
    id              int
    proposalNumber  int
    acceptedProposal *Proposal
    acceptedValue   interface{}
    promises        map[int]*Promise
}

type Proposal struct {
    Number int
    Value  interface{}
}

type Promise struct {
    ProposalNumber int
    AcceptedProposal *Proposal
}

// Phase 1: Prepare
func (pn *PaxosNode) prepare(proposalNumber int) []*Promise {
    pn.proposalNumber = proposalNumber
    promises := make([]*Promise, 0)
    
    for _, peer := range pn.peers {
        promise := pn.sendPrepare(peer, proposalNumber)
        if promise != nil {
            promises = append(promises, promise)
        }
    }
    
    return promises
}

// Phase 2: Accept
func (pn *PaxosNode) accept(proposal *Proposal) bool {
    acceptCount := 0
    
    for _, peer := range pn.peers {
        if pn.sendAccept(peer, proposal) {
            acceptCount++
        }
    }
    
    return acceptCount > len(pn.peers)/2
}

// 完整的Paxos提议过程
func (pn *PaxosNode) propose(value interface{}) bool {
    proposalNumber := pn.generateProposalNumber()
    
    // Phase 1: Prepare
    promises := pn.prepare(proposalNumber)
    if len(promises) <= len(pn.peers)/2 {
        return false // 未获得大多数承诺
    }
    
    // 选择值
    proposalValue := value
    maxProposalNumber := -1
    
    for _, promise := range promises {
        if promise.AcceptedProposal != nil &&
           promise.AcceptedProposal.Number > maxProposalNumber {
            maxProposalNumber = promise.AcceptedProposal.Number
            proposalValue = promise.AcceptedProposal.Value
        }
    }
    
    // Phase 2: Accept
    proposal := &Proposal{
        Number: proposalNumber,
        Value:  proposalValue,
    }
    
    return pn.accept(proposal)
}
```

## 5. 分布式系统设计模式

### 5.1 分片模式 (Sharding)
```go
type ShardingStrategy interface {
    GetShard(key string) int
    GetShardCount() int
}

// 哈希分片
type HashSharding struct {
    shardCount int
}

func (hs *HashSharding) GetShard(key string) int {
    hash := fnv.New32a()
    hash.Write([]byte(key))
    return int(hash.Sum32()) % hs.shardCount
}

// 范围分片
type RangeSharding struct {
    ranges []ShardRange
}

type ShardRange struct {
    Start string
    End   string
    Shard int
}

func (rs *RangeSharding) GetShard(key string) int {
    for _, r := range rs.ranges {
        if key >= r.Start && key < r.End {
            return r.Shard
        }
    }
    return 0
}

// 分片管理器
type ShardManager struct {
    strategy ShardingStrategy
    shards   map[int]*Shard
}

type Shard struct {
    ID       int
    Replicas []string
    Primary  string
}

func (sm *ShardManager) Route(key string) *Shard {
    shardID := sm.strategy.GetShard(key)
    return sm.shards[shardID]
}
```

### 5.2 复制模式 (Replication)
```go
// 主从复制
type MasterSlaveReplication struct {
    master *Node
    slaves []*Node
}

func (msr *MasterSlaveReplication) Write(key, value string) error {
    // 写入主节点
    if err := msr.master.Write(key, value); err != nil {
        return err
    }
    
    // 异步复制到从节点
    for _, slave := range msr.slaves {
        go slave.Write(key, value)
    }
    
    return nil
}

func (msr *MasterSlaveReplication) Read(key string) (string, error) {
    // 可以从任意节点读取
    for _, node := range append([]*Node{msr.master}, msr.slaves...) {
        if value, err := node.Read(key); err == nil {
            return value, nil
        }
    }
    return "", fmt.Errorf("key not found")
}

// 多主复制
type MultiMasterReplication struct {
    masters []*Node
}

func (mmr *MultiMasterReplication) Write(key, value string) error {
    // 写入所有主节点
    errors := make([]error, 0)
    
    for _, master := range mmr.masters {
        if err := master.Write(key, value); err != nil {
            errors = append(errors, err)
        }
    }
    
    // 如果大多数成功则认为写入成功
    if len(errors) < len(mmr.masters)/2 {
        return nil
    }
    
    return fmt.Errorf("write failed on majority of masters")
}
```

## 6. 面试重点问题

### Q1: CAP定理的理解和应用？
**答案**：
- **理论**：分布式系统最多只能同时保证一致性、可用性、分区容错中的两个
- **实践**：网络分区不可避免，通常在CP和AP之间选择
- **应用**：金融系统选CP，社交网络选AP

### Q2: 如何设计一个分布式缓存系统？
**答案**：
- **分片策略**：一致性哈希解决节点增减问题
- **复制策略**：主从复制保证高可用
- **一致性**：最终一致性，异步同步
- **故障处理**：节点故障时自动切换

### Q3: Raft和Paxos的区别？
**答案**：
- **Raft**：更容易理解和实现，有明确的Leader
- **Paxos**：更通用但复杂，无固定Leader
- **应用**：Raft用于日志复制，Paxos用于配置管理

### Q4: 如何处理分布式事务？
**答案**：
- **2PC/3PC**：强一致性但性能差
- **Saga模式**：长事务的补偿机制
- **TCC模式**：Try-Confirm-Cancel三阶段
- **最终一致性**：异步消息保证最终一致

分布式系统设计需要在一致性、可用性、性能之间做权衡，理解这些原则对设计可靠的分布式系统至关重要。
