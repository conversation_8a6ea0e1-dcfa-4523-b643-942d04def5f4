# 并发控制机制详解

## 1. 乐观锁与悲观锁

### 1.1 乐观锁（Optimistic Locking）

**核心思想**：假设数据在大多数情况下不会发生冲突，因此在操作数据时不加锁，而是在更新数据时通过版本号或时间戳来检测是否有其他线程修改过数据。

#### 实现方式

##### 版本号机制
```sql
-- 查询数据
SELECT stock, version FROM products WHERE id = 1;

-- 更新数据时检查版本号
UPDATE products 
SET stock = stock - 1, version = version + 1 
WHERE id = 1 AND version = 10;
```

##### Go语言实现示例
```go
package main

import (
    "fmt"
    "sync"
    "sync/atomic"
    "time"
)

type OptimisticCounter struct {
    value   int64
    version int64
}

func (oc *OptimisticCounter) Get() (int64, int64) {
    return atomic.LoadInt64(&oc.value), atomic.LoadInt64(&oc.version)
}

func (oc *OptimisticCounter) CompareAndSwap(expectedValue, newValue, expectedVersion int64) bool {
    for {
        currentValue := atomic.LoadInt64(&oc.value)
        currentVersion := atomic.LoadInt64(&oc.version)
        
        if currentValue != expectedValue || currentVersion != expectedVersion {
            return false
        }
        
        if atomic.CompareAndSwapInt64(&oc.value, expectedValue, newValue) {
            atomic.AddInt64(&oc.version, 1)
            return true
        }
    }
}

// 乐观锁实战：库存扣减
func optimisticStockDeduction() {
    counter := &OptimisticCounter{value: 100, version: 0}
    var wg sync.WaitGroup
    successCount := int64(0)
    
    // 模拟100个并发请求扣减库存
    for i := 0; i < 100; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            
            maxRetries := 10
            for retry := 0; retry < maxRetries; retry++ {
                value, version := counter.Get()
                if value <= 0 {
                    fmt.Printf("Goroutine %d: 库存不足\n", id)
                    return
                }
                
                if counter.CompareAndSwap(value, value-1, version) {
                    atomic.AddInt64(&successCount, 1)
                    fmt.Printf("Goroutine %d: 成功扣减库存，剩余: %d\n", id, value-1)
                    return
                }
                
                // 重试前等待随机时间
                time.Sleep(time.Microsecond * time.Duration(retry*10))
            }
            fmt.Printf("Goroutine %d: 重试次数超限\n", id)
        }(i)
    }
    
    wg.Wait()
    finalValue, _ := counter.Get()
    fmt.Printf("最终库存: %d, 成功扣减次数: %d\n", finalValue, successCount)
}
```

#### 优缺点分析
- **优点**：
  - 无需加锁，性能较高
  - 避免了死锁问题
  - 适用于读多写少的场景
- **缺点**：
  - 高并发写入时可能频繁重试
  - ABA问题（需要版本号解决）

### 1.2 悲观锁（Pessimistic Locking）

**核心思想**：假设数据在大多数情况下会发生冲突，因此在操作数据时会加锁，确保其他线程无法同时操作该数据。

#### Go语言实现示例
```go
type PessimisticCounter struct {
    value int64
    mutex sync.Mutex
}

func (pc *PessimisticCounter) Get() int64 {
    pc.mutex.Lock()
    defer pc.mutex.Unlock()
    return pc.value
}

func (pc *PessimisticCounter) Decrement() bool {
    pc.mutex.Lock()
    defer pc.mutex.Unlock()
    
    if pc.value <= 0 {
        return false
    }
    
    pc.value--
    return true
}

// 悲观锁实战：银行转账
func pessimisticBankTransfer() {
    type Account struct {
        id      int
        balance int64
        mutex   sync.Mutex
    }
    
    transfer := func(from, to *Account, amount int64) error {
        // 按ID顺序加锁，避免死锁
        if from.id < to.id {
            from.mutex.Lock()
            defer from.mutex.Unlock()
            to.mutex.Lock()
            defer to.mutex.Unlock()
        } else {
            to.mutex.Lock()
            defer to.mutex.Unlock()
            from.mutex.Lock()
            defer from.mutex.Unlock()
        }
        
        if from.balance < amount {
            return fmt.Errorf("余额不足")
        }
        
        from.balance -= amount
        to.balance += amount
        return nil
    }
    
    account1 := &Account{id: 1, balance: 1000}
    account2 := &Account{id: 2, balance: 1000}
    
    var wg sync.WaitGroup
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func(i int) {
            defer wg.Done()
            if err := transfer(account1, account2, 100); err != nil {
                fmt.Printf("转账失败: %v\n", err)
            } else {
                fmt.Printf("转账成功: 账户1余额=%d, 账户2余额=%d\n", 
                    account1.balance, account2.balance)
            }
        }(i)
    }
    wg.Wait()
}
```

## 2. 锁的分类与实现

### 2.1 互斥锁（Mutex）
```go
// 基本互斥锁使用
var mu sync.Mutex
var counter int

func increment() {
    mu.Lock()
    defer mu.Unlock()
    counter++
}
```

### 2.2 读写锁（RWMutex）
```go
type SafeMap struct {
    mu   sync.RWMutex
    data map[string]int
}

func (sm *SafeMap) Get(key string) (int, bool) {
    sm.mu.RLock()
    defer sm.mu.RUnlock()
    val, ok := sm.data[key]
    return val, ok
}

func (sm *SafeMap) Set(key string, value int) {
    sm.mu.Lock()
    defer sm.mu.Unlock()
    sm.data[key] = value
}
```

### 2.3 自旋锁实现
```go
type SpinLock struct {
    flag int32
}

func (sl *SpinLock) Lock() {
    for !atomic.CompareAndSwapInt32(&sl.flag, 0, 1) {
        runtime.Gosched() // 让出CPU时间片
    }
}

func (sl *SpinLock) Unlock() {
    atomic.StoreInt32(&sl.flag, 0)
}
```

## 3. 死锁检测与预防

### 3.1 死锁的四个必要条件
1. **互斥条件**：资源不能被多个进程同时使用
2. **占有和等待条件**：进程已获得资源，同时等待其他资源
3. **不可抢占条件**：资源不能被强制释放
4. **循环等待条件**：存在进程资源的循环等待链

### 3.2 死锁预防策略
```go
// 策略1：按顺序加锁
func transferWithOrder(from, to *Account, amount int64) {
    if from.id < to.id {
        from.mutex.Lock()
        defer from.mutex.Unlock()
        to.mutex.Lock()
        defer to.mutex.Unlock()
    } else {
        to.mutex.Lock()
        defer to.mutex.Unlock()
        from.mutex.Lock()
        defer from.mutex.Unlock()
    }
    // 执行转账逻辑
}

// 策略2：超时机制
func tryLockWithTimeout(mu *sync.Mutex, timeout time.Duration) bool {
    done := make(chan bool, 1)
    go func() {
        mu.Lock()
        done <- true
    }()
    
    select {
    case <-done:
        return true
    case <-time.After(timeout):
        return false
    }
}
```

## 4. 面试常见问题

### Q1: 乐观锁和悲观锁的选择原则？
- **读多写少**：选择乐观锁
- **写多读少**：选择悲观锁
- **对一致性要求极高**：选择悲观锁
- **性能要求高**：选择乐观锁

### Q2: 如何避免死锁？
1. 按固定顺序获取锁
2. 设置锁超时时间
3. 使用死锁检测算法
4. 避免嵌套锁

### Q3: CAS操作的ABA问题如何解决？
使用版本号或时间戳标记，确保数据的唯一性标识。

### Q4: 自旋锁适用场景？
- 锁持有时间短
- CPU核心数多
- 避免线程切换开销

## 5. 实战案例分析

### 案例1：高并发计数器
```go
// 使用原子操作实现无锁计数器
type AtomicCounter struct {
    value int64
}

func (ac *AtomicCounter) Increment() int64 {
    return atomic.AddInt64(&ac.value, 1)
}

func (ac *AtomicCounter) Get() int64 {
    return atomic.LoadInt64(&ac.value)
}
```

### 案例2：生产者消费者模式
```go
func producerConsumer() {
    buffer := make(chan int, 10)
    var wg sync.WaitGroup
    
    // 生产者
    for i := 0; i < 3; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            for j := 0; j < 10; j++ {
                buffer <- id*10 + j
                fmt.Printf("Producer %d produced: %d\n", id, id*10+j)
            }
        }(i)
    }
    
    // 消费者
    for i := 0; i < 2; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            for j := 0; j < 15; j++ {
                item := <-buffer
                fmt.Printf("Consumer %d consumed: %d\n", id, item)
            }
        }(i)
    }
    
    wg.Wait()
}
```

这些并发控制机制是构建高性能、高可靠性系统的基础，理解其原理和适用场景对于系统设计至关重要。
