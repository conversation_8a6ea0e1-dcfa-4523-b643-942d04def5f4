# 算法复杂度分析

## 1. 复杂度分析基础

### 1.1 什么是算法复杂度
算法复杂度是衡量算法效率的重要指标，包括时间复杂度和空间复杂度。

**时间复杂度**：算法执行时间与输入规模的关系
**空间复杂度**：算法占用内存空间与输入规模的关系

### 1.2 大O表示法
```go
// O(1) - 常数时间复杂度
func getFirst(arr []int) int {
    if len(arr) == 0 {
        return -1
    }
    return arr[0] // 无论数组多大，都是一次操作
}

// O(n) - 线性时间复杂度
func linearSearch(arr []int, target int) int {
    for i, v := range arr {
        if v == target {
            return i
        }
    }
    return -1 // 最坏情况需要遍历整个数组
}

// O(log n) - 对数时间复杂度
func binarySearch(arr []int, target int) int {
    left, right := 0, len(arr)-1
    
    for left <= right {
        mid := left + (right-left)/2
        if arr[mid] == target {
            return mid
        } else if arr[mid] < target {
            left = mid + 1
        } else {
            right = mid - 1
        }
    }
    return -1 // 每次操作都将搜索范围减半
}

// O(n²) - 平方时间复杂度
func bubbleSort(arr []int) {
    n := len(arr)
    for i := 0; i < n-1; i++ {
        for j := 0; j < n-i-1; j++ {
            if arr[j] > arr[j+1] {
                arr[j], arr[j+1] = arr[j+1], arr[j]
            }
        }
    } // 双重循环，时间复杂度为O(n²)
}

// O(n log n) - 线性对数时间复杂度
func mergeSort(arr []int) []int {
    if len(arr) <= 1 {
        return arr
    }
    
    mid := len(arr) / 2
    left := mergeSort(arr[:mid])   // T(n/2)
    right := mergeSort(arr[mid:])  // T(n/2)
    
    return merge(left, right)      // O(n)
}
// 递推关系：T(n) = 2T(n/2) + O(n) = O(n log n)
```

## 2. 常见复杂度分析

### 2.1 递归算法复杂度
```go
// 斐波那契数列 - 指数时间复杂度 O(2^n)
func fibonacciRecursive(n int) int {
    if n <= 1 {
        return n
    }
    return fibonacciRecursive(n-1) + fibonacciRecursive(n-2)
}

// 优化版本 - 线性时间复杂度 O(n)
func fibonacciDP(n int) int {
    if n <= 1 {
        return n
    }
    
    dp := make([]int, n+1)
    dp[0], dp[1] = 0, 1
    
    for i := 2; i <= n; i++ {
        dp[i] = dp[i-1] + dp[i-2]
    }
    
    return dp[n]
}

// 空间优化版本 - O(n)时间，O(1)空间
func fibonacciOptimized(n int) int {
    if n <= 1 {
        return n
    }
    
    prev, curr := 0, 1
    for i := 2; i <= n; i++ {
        prev, curr = curr, prev+curr
    }
    
    return curr
}
```

### 2.2 分治算法复杂度
```go
// 快速排序 - 平均O(n log n)，最坏O(n²)
func quickSort(arr []int, low, high int) {
    if low < high {
        pi := partition(arr, low, high)  // O(n)
        quickSort(arr, low, pi-1)        // T(k)
        quickSort(arr, pi+1, high)       // T(n-k-1)
    }
}

func partition(arr []int, low, high int) int {
    pivot := arr[high]
    i := low - 1
    
    for j := low; j < high; j++ {
        if arr[j] < pivot {
            i++
            arr[i], arr[j] = arr[j], arr[i]
        }
    }
    
    arr[i+1], arr[high] = arr[high], arr[i+1]
    return i + 1
}

// 最大子数组和 - 分治法 O(n log n)
func maxSubarrayDivide(arr []int, low, high int) int {
    if low == high {
        return arr[low]
    }
    
    mid := (low + high) / 2
    
    leftSum := maxSubarrayDivide(arr, low, mid)
    rightSum := maxSubarrayDivide(arr, mid+1, high)
    crossSum := maxCrossingSum(arr, low, mid, high)
    
    return max(leftSum, max(rightSum, crossSum))
}

func maxCrossingSum(arr []int, low, mid, high int) int {
    leftSum := math.MinInt32
    sum := 0
    for i := mid; i >= low; i-- {
        sum += arr[i]
        if sum > leftSum {
            leftSum = sum
        }
    }
    
    rightSum := math.MinInt32
    sum = 0
    for i := mid + 1; i <= high; i++ {
        sum += arr[i]
        if sum > rightSum {
            rightSum = sum
        }
    }
    
    return leftSum + rightSum
}
```

## 3. 空间复杂度分析

### 3.1 递归调用栈
```go
// 阶乘计算 - O(n)空间复杂度（递归栈）
func factorialRecursive(n int) int {
    if n <= 1 {
        return 1
    }
    return n * factorialRecursive(n-1) // 需要n层递归栈
}

// 迭代版本 - O(1)空间复杂度
func factorialIterative(n int) int {
    result := 1
    for i := 2; i <= n; i++ {
        result *= i
    }
    return result
}
```

### 3.2 辅助数据结构
```go
// 计数排序 - O(k)空间复杂度，k为数据范围
func countingSort(arr []int) []int {
    if len(arr) == 0 {
        return arr
    }
    
    // 找到最大值确定计数数组大小
    maxVal := arr[0]
    for _, v := range arr {
        if v > maxVal {
            maxVal = v
        }
    }
    
    // 创建计数数组 - O(k)空间
    count := make([]int, maxVal+1)
    
    // 计数
    for _, v := range arr {
        count[v]++
    }
    
    // 重构数组
    result := make([]int, len(arr))
    index := 0
    for i, c := range count {
        for j := 0; j < c; j++ {
            result[index] = i
            index++
        }
    }
    
    return result
}
```

## 4. 摊还分析

### 4.1 动态数组扩容
```go
type DynamicArray struct {
    data     []int
    size     int
    capacity int
}

func NewDynamicArray() *DynamicArray {
    return &DynamicArray{
        data:     make([]int, 1),
        size:     0,
        capacity: 1,
    }
}

// 摊还时间复杂度为O(1)
func (da *DynamicArray) Append(value int) {
    if da.size == da.capacity {
        // 扩容操作 - 单次O(n)，但摊还后为O(1)
        da.resize()
    }
    
    da.data[da.size] = value
    da.size++
}

func (da *DynamicArray) resize() {
    newCapacity := da.capacity * 2
    newData := make([]int, newCapacity)
    
    // 复制原数据 - O(n)
    copy(newData, da.data)
    
    da.data = newData
    da.capacity = newCapacity
}
```

### 4.2 摊还分析证明
```
动态数组扩容的摊还分析：
- 假设进行n次插入操作
- 扩容发生在容量为1, 2, 4, 8, ..., 2^k时
- 总的复制操作次数：1 + 2 + 4 + ... + 2^k ≈ 2n
- 平均每次插入的摊还成本：2n/n = 2 = O(1)
```

## 5. 复杂度优化技巧

### 5.1 空间换时间
```go
// 两数之和 - 暴力解法O(n²)时间，O(1)空间
func twoSumBrute(nums []int, target int) []int {
    for i := 0; i < len(nums); i++ {
        for j := i + 1; j < len(nums); j++ {
            if nums[i]+nums[j] == target {
                return []int{i, j}
            }
        }
    }
    return nil
}

// 哈希表优化 - O(n)时间，O(n)空间
func twoSumHash(nums []int, target int) []int {
    numMap := make(map[int]int)
    
    for i, num := range nums {
        complement := target - num
        if j, exists := numMap[complement]; exists {
            return []int{j, i}
        }
        numMap[num] = i
    }
    
    return nil
}
```

### 5.2 预处理优化
```go
// 区间和查询 - 前缀和优化
type PrefixSum struct {
    prefixSum []int
}

func NewPrefixSum(nums []int) *PrefixSum {
    prefixSum := make([]int, len(nums)+1)
    
    // 预处理 - O(n)时间
    for i, num := range nums {
        prefixSum[i+1] = prefixSum[i] + num
    }
    
    return &PrefixSum{prefixSum: prefixSum}
}

// 查询区间和 - O(1)时间
func (ps *PrefixSum) RangeSum(left, right int) int {
    return ps.prefixSum[right+1] - ps.prefixSum[left]
}
```

## 6. 面试重点问题

### Q1: 如何分析递归算法的时间复杂度？
**答案**：
1. **建立递推关系**：T(n) = aT(n/b) + f(n)
2. **使用主定理**：比较f(n)与n^(log_b a)的大小关系
3. **递归树方法**：画出递归调用树，计算总工作量
4. **替换法**：猜测解的形式，用数学归纳法证明

### Q2: 什么是摊还分析？
**答案**：
- **定义**：分析一系列操作的平均时间复杂度
- **方法**：聚合分析、记账法、势能法
- **应用**：动态数组、并查集、斐波那契堆

### Q3: 如何优化算法复杂度？
**答案**：
- **空间换时间**：使用哈希表、缓存等
- **预处理**：前缀和、后缀积等
- **分治策略**：将大问题分解为小问题
- **动态规划**：避免重复计算

### Q4: 常见复杂度的大小关系？
**答案**：
O(1) < O(log n) < O(n) < O(n log n) < O(n²) < O(n³) < O(2^n) < O(n!)

理解算法复杂度是算法设计和优化的基础，也是面试中的重要考点。
