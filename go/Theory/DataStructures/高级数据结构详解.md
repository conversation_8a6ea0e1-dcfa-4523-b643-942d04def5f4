# 高级数据结构详解

## 1. 树形数据结构

### 1.1 平衡二叉搜索树 (AVL树)
```go
type AVLNode struct {
    Key    int
    Height int
    Left   *AVLNode
    Right  *AVLNode
}

type AVLTree struct {
    Root *AVLNode
}

// 获取节点高度
func (node *AVLNode) getHeight() int {
    if node == nil {
        return 0
    }
    return node.Height
}

// 更新节点高度
func (node *AVLNode) updateHeight() {
    leftHeight := 0
    rightHeight := 0
    
    if node.Left != nil {
        leftHeight = node.Left.Height
    }
    if node.Right != nil {
        rightHeight = node.Right.Height
    }
    
    node.Height = max(leftHeight, rightHeight) + 1
}

// 获取平衡因子
func (node *AVLNode) getBalance() int {
    if node == nil {
        return 0
    }
    
    leftHeight := 0
    rightHeight := 0
    
    if node.Left != nil {
        leftHeight = node.Left.Height
    }
    if node.Right != nil {
        rightHeight = node.Right.Height
    }
    
    return leftHeight - rightHeight
}

// 右旋转
func (tree *AVLTree) rightRotate(y *AVLNode) *AVLNode {
    x := y.Left
    T2 := x.Right
    
    // 执行旋转
    x.Right = y
    y.Left = T2
    
    // 更新高度
    y.updateHeight()
    x.updateHeight()
    
    return x
}

// 左旋转
func (tree *AVLTree) leftRotate(x *AVLNode) *AVLNode {
    y := x.Right
    T2 := y.Left
    
    // 执行旋转
    y.Left = x
    x.Right = T2
    
    // 更新高度
    x.updateHeight()
    y.updateHeight()
    
    return y
}

// 插入节点
func (tree *AVLTree) insert(node *AVLNode, key int) *AVLNode {
    // 1. 执行标准BST插入
    if node == nil {
        return &AVLNode{Key: key, Height: 1}
    }
    
    if key < node.Key {
        node.Left = tree.insert(node.Left, key)
    } else if key > node.Key {
        node.Right = tree.insert(node.Right, key)
    } else {
        return node // 重复键值，不插入
    }
    
    // 2. 更新当前节点高度
    node.updateHeight()
    
    // 3. 获取平衡因子
    balance := node.getBalance()
    
    // 4. 如果不平衡，进行旋转
    // Left Left Case
    if balance > 1 && key < node.Left.Key {
        return tree.rightRotate(node)
    }
    
    // Right Right Case
    if balance < -1 && key > node.Right.Key {
        return tree.leftRotate(node)
    }
    
    // Left Right Case
    if balance > 1 && key > node.Left.Key {
        node.Left = tree.leftRotate(node.Left)
        return tree.rightRotate(node)
    }
    
    // Right Left Case
    if balance < -1 && key < node.Right.Key {
        node.Right = tree.rightRotate(node.Right)
        return tree.leftRotate(node)
    }
    
    return node
}
```

### 1.2 红黑树
```go
type Color int

const (
    RED Color = iota
    BLACK
)

type RBNode struct {
    Key    int
    Color  Color
    Left   *RBNode
    Right  *RBNode
    Parent *RBNode
}

type RedBlackTree struct {
    Root *RBNode
    NIL  *RBNode // 哨兵节点
}

func NewRedBlackTree() *RedBlackTree {
    nil_node := &RBNode{Color: BLACK}
    return &RedBlackTree{
        Root: nil_node,
        NIL:  nil_node,
    }
}

// 左旋转
func (rbt *RedBlackTree) leftRotate(x *RBNode) {
    y := x.Right
    x.Right = y.Left
    
    if y.Left != rbt.NIL {
        y.Left.Parent = x
    }
    
    y.Parent = x.Parent
    
    if x.Parent == rbt.NIL {
        rbt.Root = y
    } else if x == x.Parent.Left {
        x.Parent.Left = y
    } else {
        x.Parent.Right = y
    }
    
    y.Left = x
    x.Parent = y
}

// 插入修复
func (rbt *RedBlackTree) insertFixup(z *RBNode) {
    for z.Parent.Color == RED {
        if z.Parent == z.Parent.Parent.Left {
            y := z.Parent.Parent.Right
            if y.Color == RED {
                // Case 1: 叔叔节点是红色
                z.Parent.Color = BLACK
                y.Color = BLACK
                z.Parent.Parent.Color = RED
                z = z.Parent.Parent
            } else {
                if z == z.Parent.Right {
                    // Case 2: 叔叔是黑色，z是右孩子
                    z = z.Parent
                    rbt.leftRotate(z)
                }
                // Case 3: 叔叔是黑色，z是左孩子
                z.Parent.Color = BLACK
                z.Parent.Parent.Color = RED
                rbt.rightRotate(z.Parent.Parent)
            }
        } else {
            // 对称情况
            y := z.Parent.Parent.Left
            if y.Color == RED {
                z.Parent.Color = BLACK
                y.Color = BLACK
                z.Parent.Parent.Color = RED
                z = z.Parent.Parent
            } else {
                if z == z.Parent.Left {
                    z = z.Parent
                    rbt.rightRotate(z)
                }
                z.Parent.Color = BLACK
                z.Parent.Parent.Color = RED
                rbt.leftRotate(z.Parent.Parent)
            }
        }
    }
    rbt.Root.Color = BLACK
}
```

## 2. 堆数据结构

### 2.1 二项堆
```go
type BinomialNode struct {
    Key    int
    Degree int
    Parent *BinomialNode
    Child  *BinomialNode
    Sibling *BinomialNode
}

type BinomialHeap struct {
    Head *BinomialNode
}

// 合并两个二项堆
func (bh *BinomialHeap) Union(other *BinomialHeap) *BinomialHeap {
    newHeap := &BinomialHeap{}
    newHeap.Head = bh.merge(bh.Head, other.Head)
    
    if newHeap.Head == nil {
        return newHeap
    }
    
    // 合并相同度数的树
    prev := nil
    curr := newHeap.Head
    next := curr.Sibling
    
    for next != nil {
        if curr.Degree != next.Degree ||
           (next.Sibling != nil && next.Sibling.Degree == curr.Degree) {
            prev = curr
            curr = next
        } else if curr.Key <= next.Key {
            curr.Sibling = next.Sibling
            bh.link(next, curr)
        } else {
            if prev == nil {
                newHeap.Head = next
            } else {
                prev.Sibling = next
            }
            bh.link(curr, next)
            curr = next
        }
        next = curr.Sibling
    }
    
    return newHeap
}

// 链接两个相同度数的二项树
func (bh *BinomialHeap) link(y, z *BinomialNode) {
    y.Parent = z
    y.Sibling = z.Child
    z.Child = y
    z.Degree++
}
```

### 2.2 斐波那契堆
```go
type FibNode struct {
    Key    int
    Degree int
    Mark   bool
    Parent *FibNode
    Child  *FibNode
    Left   *FibNode
    Right  *FibNode
}

type FibonacciHeap struct {
    Min *FibNode
    N   int // 节点数量
}

// 插入节点 - O(1)
func (fh *FibonacciHeap) Insert(key int) *FibNode {
    node := &FibNode{Key: key}
    node.Left = node
    node.Right = node
    
    if fh.Min == nil {
        fh.Min = node
    } else {
        fh.insertToRootList(node)
        if node.Key < fh.Min.Key {
            fh.Min = node
        }
    }
    
    fh.N++
    return node
}

// 提取最小值 - 摊还O(log n)
func (fh *FibonacciHeap) ExtractMin() *FibNode {
    z := fh.Min
    if z != nil {
        // 将z的所有子节点添加到根链表
        if z.Child != nil {
            child := z.Child
            for {
                next := child.Right
                fh.insertToRootList(child)
                child.Parent = nil
                child = next
                if child == z.Child {
                    break
                }
            }
        }
        
        // 从根链表中移除z
        fh.removeFromRootList(z)
        
        if z == z.Right {
            fh.Min = nil
        } else {
            fh.Min = z.Right
            fh.consolidate()
        }
        
        fh.N--
    }
    
    return z
}

// 减少键值 - 摊还O(1)
func (fh *FibonacciHeap) DecreaseKey(x *FibNode, k int) {
    if k > x.Key {
        panic("new key is greater than current key")
    }
    
    x.Key = k
    y := x.Parent
    
    if y != nil && x.Key < y.Key {
        fh.cut(x, y)
        fh.cascadingCut(y)
    }
    
    if x.Key < fh.Min.Key {
        fh.Min = x
    }
}

// 级联切割
func (fh *FibonacciHeap) cascadingCut(y *FibNode) {
    z := y.Parent
    if z != nil {
        if !y.Mark {
            y.Mark = true
        } else {
            fh.cut(y, z)
            fh.cascadingCut(z)
        }
    }
}
```

## 3. 图数据结构

### 3.1 并查集 (Union-Find)
```go
type UnionFind struct {
    parent []int
    rank   []int
    count  int
}

func NewUnionFind(n int) *UnionFind {
    parent := make([]int, n)
    rank := make([]int, n)
    
    for i := 0; i < n; i++ {
        parent[i] = i
        rank[i] = 0
    }
    
    return &UnionFind{
        parent: parent,
        rank:   rank,
        count:  n,
    }
}

// 查找根节点（路径压缩）
func (uf *UnionFind) Find(x int) int {
    if uf.parent[x] != x {
        uf.parent[x] = uf.Find(uf.parent[x]) // 路径压缩
    }
    return uf.parent[x]
}

// 合并两个集合（按秩合并）
func (uf *UnionFind) Union(x, y int) {
    rootX := uf.Find(x)
    rootY := uf.Find(y)
    
    if rootX != rootY {
        // 按秩合并
        if uf.rank[rootX] < uf.rank[rootY] {
            uf.parent[rootX] = rootY
        } else if uf.rank[rootX] > uf.rank[rootY] {
            uf.parent[rootY] = rootX
        } else {
            uf.parent[rootY] = rootX
            uf.rank[rootX]++
        }
        uf.count--
    }
}

// 判断是否连通
func (uf *UnionFind) Connected(x, y int) bool {
    return uf.Find(x) == uf.Find(y)
}
```

### 3.2 字典树 (Trie)
```go
type TrieNode struct {
    children map[rune]*TrieNode
    isEnd    bool
    count    int // 以此节点为前缀的单词数量
}

type Trie struct {
    root *TrieNode
}

func NewTrie() *Trie {
    return &Trie{
        root: &TrieNode{
            children: make(map[rune]*TrieNode),
        },
    }
}

// 插入单词
func (t *Trie) Insert(word string) {
    node := t.root
    
    for _, char := range word {
        if _, exists := node.children[char]; !exists {
            node.children[char] = &TrieNode{
                children: make(map[rune]*TrieNode),
            }
        }
        node = node.children[char]
        node.count++
    }
    
    node.isEnd = true
}

// 搜索单词
func (t *Trie) Search(word string) bool {
    node := t.root
    
    for _, char := range word {
        if _, exists := node.children[char]; !exists {
            return false
        }
        node = node.children[char]
    }
    
    return node.isEnd
}

// 前缀搜索
func (t *Trie) StartsWith(prefix string) bool {
    node := t.root
    
    for _, char := range prefix {
        if _, exists := node.children[char]; !exists {
            return false
        }
        node = node.children[char]
    }
    
    return true
}

// 获取以prefix为前缀的单词数量
func (t *Trie) CountWordsWithPrefix(prefix string) int {
    node := t.root
    
    for _, char := range prefix {
        if _, exists := node.children[char]; !exists {
            return 0
        }
        node = node.children[char]
    }
    
    return node.count
}
```

## 4. 高级哈希结构

### 4.1 布隆过滤器
```go
import (
    "hash/fnv"
    "math"
)

type BloomFilter struct {
    bitSet []bool
    size   uint
    hashFuncs int
}

func NewBloomFilter(expectedElements int, falsePositiveRate float64) *BloomFilter {
    size := uint(-float64(expectedElements) * math.Log(falsePositiveRate) / (math.Log(2) * math.Log(2)))
    hashFuncs := int(float64(size) / float64(expectedElements) * math.Log(2))
    
    return &BloomFilter{
        bitSet:    make([]bool, size),
        size:      size,
        hashFuncs: hashFuncs,
    }
}

// 添加元素
func (bf *BloomFilter) Add(data []byte) {
    for i := 0; i < bf.hashFuncs; i++ {
        hash := bf.hash(data, uint(i))
        bf.bitSet[hash%bf.size] = true
    }
}

// 检查元素是否可能存在
func (bf *BloomFilter) Contains(data []byte) bool {
    for i := 0; i < bf.hashFuncs; i++ {
        hash := bf.hash(data, uint(i))
        if !bf.bitSet[hash%bf.size] {
            return false
        }
    }
    return true
}

// 哈希函数
func (bf *BloomFilter) hash(data []byte, seed uint) uint {
    h := fnv.New32a()
    h.Write(data)
    h.Write([]byte{byte(seed)})
    return uint(h.Sum32())
}
```

### 4.2 一致性哈希
```go
import (
    "crypto/sha1"
    "fmt"
    "sort"
    "strconv"
)

type ConsistentHash struct {
    replicas int
    keys     []int // 排序的哈希环
    hashMap  map[int]string // 哈希值到节点的映射
}

func NewConsistentHash(replicas int) *ConsistentHash {
    return &ConsistentHash{
        replicas: replicas,
        hashMap:  make(map[int]string),
    }
}

// 添加节点
func (ch *ConsistentHash) Add(nodes ...string) {
    for _, node := range nodes {
        for i := 0; i < ch.replicas; i++ {
            hash := ch.hashKey(node + strconv.Itoa(i))
            ch.keys = append(ch.keys, hash)
            ch.hashMap[hash] = node
        }
    }
    sort.Ints(ch.keys)
}

// 移除节点
func (ch *ConsistentHash) Remove(node string) {
    for i := 0; i < ch.replicas; i++ {
        hash := ch.hashKey(node + strconv.Itoa(i))
        idx := sort.SearchInts(ch.keys, hash)
        ch.keys = append(ch.keys[:idx], ch.keys[idx+1:]...)
        delete(ch.hashMap, hash)
    }
}

// 获取键对应的节点
func (ch *ConsistentHash) Get(key string) string {
    if len(ch.keys) == 0 {
        return ""
    }
    
    hash := ch.hashKey(key)
    idx := sort.SearchInts(ch.keys, hash)
    
    if idx == len(ch.keys) {
        idx = 0
    }
    
    return ch.hashMap[ch.keys[idx]]
}

// 哈希函数
func (ch *ConsistentHash) hashKey(key string) int {
    h := sha1.New()
    h.Write([]byte(key))
    hashBytes := h.Sum(nil)
    
    hash := 0
    for i := 0; i < 4; i++ {
        hash = hash<<8 + int(hashBytes[i])
    }
    
    return hash
}
```

## 5. 面试重点问题

### Q1: AVL树和红黑树的区别？
**答案**：
- **AVL树**：严格平衡，查找效率高，但插入删除需要更多旋转
- **红黑树**：近似平衡，插入删除效率高，广泛应用于实际系统
- **选择**：查找密集用AVL，插入删除密集用红黑树

### Q2: 并查集的时间复杂度？
**答案**：
- **路径压缩 + 按秩合并**：接近O(1)的摊还时间复杂度
- **反阿克曼函数**：α(n)增长极其缓慢，实际应用中可视为常数

### Q3: 布隆过滤器的特点？
**答案**：
- **优点**：空间效率高，查询速度快
- **缺点**：有假阳性，不能删除元素
- **应用**：缓存穿透防护、爬虫URL去重

### Q4: 一致性哈希解决什么问题？
**答案**：
- **问题**：分布式系统中节点增减导致大量数据迁移
- **解决**：通过哈希环实现数据的平滑迁移
- **优化**：虚拟节点解决数据分布不均问题

高级数据结构是解决复杂问题的重要工具，理解其原理和应用场景对系统设计至关重要。
