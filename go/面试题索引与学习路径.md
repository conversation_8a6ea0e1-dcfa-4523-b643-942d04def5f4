# Go语言面试题索引与学习路径

## 📚 目录结构概览

```
go/
├── Theory/                          # 理论基础
│   ├── ComputerScience/            # 计算机科学基础
│   ├── Concurrency/                # 并发理论
│   ├── SystemDesign/               # 系统设计理论
│   ├── Algorithms/                 # 算法理论
│   └── DataStructures/             # 数据结构理论
├── Go/                             # Go语言核心
├── Concurrency/                    # 并发编程实战
├── OperatingSystem/                # 操作系统
├── Network/                        # 网络编程
├── Database/                       # 数据库相关
├── DistributedSystems/             # 分布式系统
├── Case/                          # 实战案例
└── 面试题索引与学习路径.md          # 本文件
```

## 🎯 按难度级别分类

### 初级 (Junior Level) 🟢

#### Go语言基础
- [GPM调度模型](Go/GPM调度模型.md) - Go运行时调度机制
- [chan底层原理](Go/chan底层原理.md) - Channel实现原理
- [slice实践以及底层实现](Go/slice实践以及底层实现.md) - 切片使用和原理
- [map底层实现和并发安全](Go/map底层实现和并发安全.md) - Map实现细节
- [defer执行顺序和原理](Go/defer执行顺序和原理.md) - defer机制

#### 并发基础
- [协程和线程的区别](Theory/Concurrency/协程和线程的区别.md) - 基础概念
- [互斥锁](Theory/Concurrency/互斥锁.md) - 基本同步原语
- [Channel深度解析与最佳实践](Concurrency/Channel深度解析与最佳实践.md) - Channel使用

#### 操作系统基础
- [进程和线程之间有什么区别](OperatingSystem/进程和线程之间有什么区别.md)
- [Linux 常用命令](OperatingSystem/Linux常用命令.md)
- [孤儿进程和僵尸进程](OperatingSystem/孤儿进程和僵尸进程以及僵死进程的解决方案.md)

### 中级 (Mid Level) 🟡

#### Go语言进阶
- [Go语言高级特性与性能优化](Go/Go语言高级特性与性能优化.md) - 反射、泛型、性能调优
- [GC垃圾回收算法](Go/GC垃圾回收算法.md) - 垃圾回收机制
- [Interface内部实现的理解](Go/Interface内部实现的理解.md) - 接口实现
- [Go协程与其他语言协程线程对比](Go/Go协程与其他语言协程线程对比.md) - 对比分析

#### 并发编程
- [Goroutine与Go并发模型深度解析](Theory/Concurrency/Goroutine与Go并发模型深度解析.md)
- [Go内存模型与原子操作](Concurrency/Go内存模型与原子操作.md) - 内存模型详解
- [竞态条件检测与并发安全实战](Concurrency/竞态条件检测与并发安全实战.md)
- [高级并发编程实战案例](Concurrency/高级并发编程实战案例.md)

#### 系统编程
- [Linux系统编程与性能调优实战](OperatingSystem/Linux系统编程与性能调优实战.md)
- [CPU调度与上下文切换](OperatingSystem/CPU调度与上下文切换.md)
- [内存管理深度解析](OperatingSystem/内存管理深度解析.md)

### 高级 (Senior Level) 🔴

#### 系统设计与架构
- [操作系统核心概念与实战](Theory/ComputerScience/操作系统核心概念与实战.md)
- [并发控制机制详解](Theory/Concurrency/并发控制机制详解.md)
- [分布式系统一致性算法详解](DistributedSystems/分布式系统一致性算法详解.md)

#### 高性能编程
- [高级锁机制与同步原语](Concurrency/高级锁机制与同步原语.md)
- [并发安全与竞态条件](Concurrency/并发安全与竞态条件.md)
- [网络编程深度解析](OperatingSystem/Linux网络编程深度解析.md)

#### 实战案例
- [综合性面试场景题](Case/综合性面试场景题.md) - 复杂系统设计
- [高并发系统性能优化实战](Case/高并发系统性能优化实战.md)
- [微服务架构设计与实现](Case/微服务架构设计与实现.md)

## 🏢 按公司类型分类

### 互联网大厂 (FAANG/BAT)

#### 必备核心技能
1. **Go语言深度**
   - [GPM调度模型](Go/GPM调度模型.md)
   - [GC垃圾回收算法](Go/GC垃圾回收算法.md)
   - [Go语言高级特性与性能优化](Go/Go语言高级特性与性能优化.md)

2. **并发编程精通**
   - [Goroutine与Go并发模型深度解析](Theory/Concurrency/Goroutine与Go并发模型深度解析.md)
   - [高级并发编程实战案例](Concurrency/高级并发编程实战案例.md)
   - [竞态条件检测与并发安全实战](Concurrency/竞态条件检测与并发安全实战.md)

3. **系统设计能力**
   - [操作系统核心概念与实战](Theory/ComputerScience/操作系统核心概念与实战.md)
   - [Linux系统编程与性能调优实战](OperatingSystem/Linux系统编程与性能调优实战.md)

#### 高频面试题
- [秒杀场景](Case/秒杀场景.md) - 高并发系统设计
- [假如明天是活动高峰？QPS预计会翻10倍](Case/假如明天是活动高峰？QPS预计会翻10倍，你要怎么做？.md)
- [实现秒杀功能](Case/实现秒杀功能.md)

### 中小型公司/创业公司

#### 实用技能重点
1. **快速开发能力**
   - [常用包](Go/常用包.md) - Go标准库使用
   - [gin路由树](Go/gin路由树.md) - Web框架
   - [HTTP Client大量长连接保持](Go/HTTPClient大量长连接保持.md)

2. **问题排查能力**
   - [内存泄露的发现与排查](Go/内存泄露的发现与排查.md)
   - [goroutine泄漏检测与防范](Go/goroutine泄漏检测与防范.md)
   - [dlv分析golang进程](Go/dlv分析golang进程.md)

3. **基础架构**
   - [评论系统](Case/评论系统.md) - 常见业务场景
   - [超卖问题](Case/超卖问题.md) - 电商场景

### 金融/银行业

#### 重点关注领域
1. **数据一致性**
   - [并发控制机制详解](Theory/Concurrency/并发控制机制详解.md)
   - [乐观锁和悲观锁](Theory/Concurrency/乐观锁和悲观锁.md)
   - [分布式事务](DistributedSystems/分布式事务.md)

2. **高可用性**
   - [分布式锁及优化思路](DistributedSystems/分布式锁及优化思路.md)
   - [CAP理论](DistributedSystems/cap理论.md)
   - [Raft算法](DistributedSystems/raft算法.md)

## 📖 学习路径推荐

### 阶段一：Go语言基础 (2-4周)

```mermaid
graph TD
    A[Go语法基础] --> B[数据类型与结构]
    B --> C[函数与方法]
    C --> D[接口与多态]
    D --> E[错误处理]
    E --> F[包管理]
```

**推荐阅读顺序：**
1. [slice实践以及底层实现](Go/slice实践以及底层实现.md)
2. [map底层实现和并发安全](Go/map底层实现和并发安全.md)
3. [Interface内部实现的理解](Go/Interface内部实现的理解.md)
4. [defer执行顺序和原理](Go/defer执行顺序和原理.md)
5. [panic和recover机制](Go/panic和recover机制.md)

### 阶段二：并发编程 (3-5周)

```mermaid
graph TD
    A[Goroutine基础] --> B[Channel通信]
    B --> C[同步原语]
    C --> D[内存模型]
    D --> E[竞态检测]
    E --> F[高级模式]
```

**推荐阅读顺序：**
1. [GPM调度模型](Go/GPM调度模型.md)
2. [chan底层原理](Go/chan底层原理.md)
3. [Channel深度解析与最佳实践](Concurrency/Channel深度解析与最佳实践.md)
4. [Go内存模型与原子操作](Concurrency/Go内存模型与原子操作.md)
5. [竞态条件检测与并发安全实战](Concurrency/竞态条件检测与并发安全实战.md)
6. [高级并发编程实战案例](Concurrency/高级并发编程实战案例.md)

### 阶段三：系统编程 (4-6周)

```mermaid
graph TD
    A[操作系统基础] --> B[进程线程]
    B --> C[内存管理]
    C --> D[I/O系统]
    D --> E[网络编程]
    E --> F[性能调优]
```

**推荐阅读顺序：**
1. [进程和线程之间有什么区别](OperatingSystem/进程和线程之间有什么区别.md)
2. [CPU调度与上下文切换](OperatingSystem/CPU调度与上下文切换.md)
3. [内存管理深度解析](OperatingSystem/内存管理深度解析.md)
4. [Linux系统编程与性能调优实战](OperatingSystem/Linux系统编程与性能调优实战.md)
5. [操作系统核心概念与实战](Theory/ComputerScience/操作系统核心概念与实战.md)

### 阶段四：分布式系统 (4-6周)

```mermaid
graph TD
    A[分布式基础] --> B[一致性算法]
    B --> C[分布式存储]
    C --> D[微服务架构]
    D --> E[系统设计]
```

**推荐阅读顺序：**
1. [CAP理论](DistributedSystems/cap理论.md)
2. [Raft算法](DistributedSystems/raft算法.md)
3. [分布式锁及优化思路](DistributedSystems/分布式锁及优化思路.md)
4. [分布式事务](DistributedSystems/分布式事务.md)
5. [微服务架构设计与实现](Case/微服务架构设计与实现.md)

### 阶段五：实战项目 (持续进行)

**项目实践：**
1. [秒杀系统](Case/秒杀场景.md) - 高并发场景
2. [评论系统](Case/评论系统.md) - 常见业务场景
3. [高并发系统性能优化](Case/高并发系统性能优化实战.md) - 性能调优

## 🔧 实用工具与技巧

### 性能分析工具
- `go tool pprof` - 性能分析
- `go run -race` - 竞态检测
- `go tool trace` - 执行跟踪
- `dlv` - 调试器

### 学习建议
1. **理论与实践结合** - 每学一个概念都要写代码验证
2. **循序渐进** - 按照推荐路径逐步深入
3. **多做练习** - 完成每个阶段的实战项目
4. **持续更新** - 关注Go语言新特性和最佳实践

### 面试准备清单
- [ ] Go语言基础语法和特性
- [ ] 并发编程和goroutine
- [ ] 内存管理和GC
- [ ] 网络编程和I/O
- [ ] 数据库操作和优化
- [ ] 分布式系统设计
- [ ] 系统设计和架构
- [ ] 算法和数据结构
- [ ] 项目经验和问题解决

## 📞 联系与反馈

如果您在学习过程中遇到问题或有改进建议，欢迎提出！

---

**祝您面试顺利，早日拿到心仪的offer！** 🎉
