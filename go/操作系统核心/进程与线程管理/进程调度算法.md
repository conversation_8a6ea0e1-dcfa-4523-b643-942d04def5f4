# 进程调度算法详解

## 1. 调度算法概述

### 1.1 什么是进程调度
进程调度是操作系统内核的核心功能，负责决定在某个时刻哪个进程获得CPU执行权。调度算法的选择直接影响系统的性能、响应时间和公平性。

### 1.2 调度的时机
```
进程调度发生的时机：
1. 进程从运行态转为等待态（如等待I/O）
2. 进程从运行态转为就绪态（如时间片用完）
3. 进程从等待态转为就绪态（如I/O完成）
4. 进程终止
```

### 1.3 调度算法评价指标
- **CPU利用率**：CPU忙碌时间的百分比
- **吞吐量**：单位时间内完成的进程数
- **周转时间**：进程从提交到完成的总时间
- **等待时间**：进程在就绪队列中等待的时间
- **响应时间**：从提交请求到首次响应的时间

## 2. 非抢占式调度算法

### 2.1 先来先服务 (FCFS)
```go
// FCFS调度算法模拟
type Process struct {
    ID          int
    ArrivalTime int
    BurstTime   int
    StartTime   int
    EndTime     int
    WaitTime    int
    TurnTime    int
}

func FCFS(processes []Process) {
    // 按到达时间排序
    sort.Slice(processes, func(i, j int) bool {
        return processes[i].ArrivalTime < processes[j].ArrivalTime
    })
    
    currentTime := 0
    for i := range processes {
        // 如果当前时间小于进程到达时间，CPU空闲
        if currentTime < processes[i].ArrivalTime {
            currentTime = processes[i].ArrivalTime
        }
        
        processes[i].StartTime = currentTime
        processes[i].EndTime = currentTime + processes[i].BurstTime
        processes[i].TurnTime = processes[i].EndTime - processes[i].ArrivalTime
        processes[i].WaitTime = processes[i].TurnTime - processes[i].BurstTime
        
        currentTime = processes[i].EndTime
    }
}

// 示例使用
func fcfsExample() {
    processes := []Process{
        {ID: 1, ArrivalTime: 0, BurstTime: 8},
        {ID: 2, ArrivalTime: 1, BurstTime: 4},
        {ID: 3, ArrivalTime: 2, BurstTime: 9},
        {ID: 4, ArrivalTime: 3, BurstTime: 5},
    }
    
    FCFS(processes)
    
    fmt.Println("FCFS调度结果:")
    for _, p := range processes {
        fmt.Printf("进程%d: 等待时间=%d, 周转时间=%d\n", 
                   p.ID, p.WaitTime, p.TurnTime)
    }
}
```

**特点**：
- 简单易实现
- 非抢占式
- 可能导致"护送效应"（短进程等待长进程）

### 2.2 最短作业优先 (SJF)
```go
func SJF(processes []Process) {
    // 按到达时间排序
    sort.Slice(processes, func(i, j int) bool {
        return processes[i].ArrivalTime < processes[j].ArrivalTime
    })
    
    completed := make([]bool, len(processes))
    currentTime := 0
    completedCount := 0
    
    for completedCount < len(processes) {
        // 找到已到达且未完成的最短作业
        shortestIdx := -1
        shortestBurst := math.MaxInt32
        
        for i := range processes {
            if !completed[i] && 
               processes[i].ArrivalTime <= currentTime &&
               processes[i].BurstTime < shortestBurst {
                shortestBurst = processes[i].BurstTime
                shortestIdx = i
            }
        }
        
        if shortestIdx == -1 {
            // 没有可执行的进程，时间前进到下一个进程到达
            minArrival := math.MaxInt32
            for i := range processes {
                if !completed[i] && processes[i].ArrivalTime < minArrival {
                    minArrival = processes[i].ArrivalTime
                }
            }
            currentTime = minArrival
            continue
        }
        
        // 执行选中的进程
        p := &processes[shortestIdx]
        p.StartTime = currentTime
        p.EndTime = currentTime + p.BurstTime
        p.TurnTime = p.EndTime - p.ArrivalTime
        p.WaitTime = p.TurnTime - p.BurstTime
        
        currentTime = p.EndTime
        completed[shortestIdx] = true
        completedCount++
    }
}
```

**特点**：
- 平均等待时间最短
- 可能导致长进程饥饿
- 需要预知进程执行时间

### 2.3 优先级调度
```go
type PriorityProcess struct {
    Process
    Priority int // 数值越小优先级越高
}

func PriorityScheduling(processes []PriorityProcess) {
    completed := make([]bool, len(processes))
    currentTime := 0
    completedCount := 0
    
    for completedCount < len(processes) {
        // 找到已到达且未完成的最高优先级进程
        highestPriorityIdx := -1
        highestPriority := math.MaxInt32
        
        for i := range processes {
            if !completed[i] && 
               processes[i].ArrivalTime <= currentTime &&
               processes[i].Priority < highestPriority {
                highestPriority = processes[i].Priority
                highestPriorityIdx = i
            }
        }
        
        if highestPriorityIdx == -1 {
            // 时间前进到下一个进程到达
            minArrival := math.MaxInt32
            for i := range processes {
                if !completed[i] && processes[i].ArrivalTime < minArrival {
                    minArrival = processes[i].ArrivalTime
                }
            }
            currentTime = minArrival
            continue
        }
        
        // 执行选中的进程
        p := &processes[highestPriorityIdx].Process
        p.StartTime = currentTime
        p.EndTime = currentTime + p.BurstTime
        p.TurnTime = p.EndTime - p.ArrivalTime
        p.WaitTime = p.TurnTime - p.BurstTime
        
        currentTime = p.EndTime
        completed[highestPriorityIdx] = true
        completedCount++
    }
}
```

## 3. 抢占式调度算法

### 3.1 时间片轮转 (RR)
```go
func RoundRobin(processes []Process, timeQuantum int) {
    queue := make([]int, 0)
    remainingTime := make([]int, len(processes))
    
    // 初始化剩余时间
    for i := range processes {
        remainingTime[i] = processes[i].BurstTime
    }
    
    currentTime := 0
    completed := 0
    
    // 将到达时间为0的进程加入队列
    for i := range processes {
        if processes[i].ArrivalTime == 0 {
            queue = append(queue, i)
        }
    }
    
    for completed < len(processes) {
        if len(queue) == 0 {
            // 队列为空，时间前进
            currentTime++
            // 检查是否有新进程到达
            for i := range processes {
                if processes[i].ArrivalTime == currentTime {
                    queue = append(queue, i)
                }
            }
            continue
        }
        
        // 取出队首进程
        processIdx := queue[0]
        queue = queue[1:]
        
        // 执行时间片
        execTime := timeQuantum
        if remainingTime[processIdx] < timeQuantum {
            execTime = remainingTime[processIdx]
        }
        
        currentTime += execTime
        remainingTime[processIdx] -= execTime
        
        // 检查新到达的进程
        for i := range processes {
            if processes[i].ArrivalTime > currentTime-execTime &&
               processes[i].ArrivalTime <= currentTime {
                queue = append(queue, i)
            }
        }
        
        // 如果进程未完成，重新加入队列
        if remainingTime[processIdx] > 0 {
            queue = append(queue, processIdx)
        } else {
            // 进程完成
            processes[processIdx].EndTime = currentTime
            processes[processIdx].TurnTime = 
                processes[processIdx].EndTime - processes[processIdx].ArrivalTime
            processes[processIdx].WaitTime = 
                processes[processIdx].TurnTime - processes[processIdx].BurstTime
            completed++
        }
    }
}
```

**特点**：
- 公平性好，每个进程都能获得CPU时间
- 响应时间好
- 时间片大小影响性能

### 3.2 最短剩余时间优先 (SRTF)
```go
func SRTF(processes []Process) {
    remainingTime := make([]int, len(processes))
    completed := make([]bool, len(processes))
    
    for i := range processes {
        remainingTime[i] = processes[i].BurstTime
    }
    
    currentTime := 0
    completedCount := 0
    
    for completedCount < len(processes) {
        // 找到已到达且剩余时间最短的进程
        shortestIdx := -1
        shortestRemaining := math.MaxInt32
        
        for i := range processes {
            if !completed[i] && 
               processes[i].ArrivalTime <= currentTime &&
               remainingTime[i] < shortestRemaining {
                shortestRemaining = remainingTime[i]
                shortestIdx = i
            }
        }
        
        if shortestIdx == -1 {
            currentTime++
            continue
        }
        
        // 执行一个时间单位
        remainingTime[shortestIdx]--
        currentTime++
        
        // 检查是否完成
        if remainingTime[shortestIdx] == 0 {
            completed[shortestIdx] = true
            completedCount++
            
            processes[shortestIdx].EndTime = currentTime
            processes[shortestIdx].TurnTime = 
                processes[shortestIdx].EndTime - processes[shortestIdx].ArrivalTime
            processes[shortestIdx].WaitTime = 
                processes[shortestIdx].TurnTime - processes[shortestIdx].BurstTime
        }
    }
}
```

## 4. 多级队列调度

### 4.1 多级反馈队列
```go
type MultiLevelQueue struct {
    queues      [][]int  // 多个优先级队列
    timeQuantum []int    // 每个队列的时间片
    processes   []Process
}

func (mlq *MultiLevelQueue) Schedule() {
    remainingTime := make([]int, len(mlq.processes))
    for i := range mlq.processes {
        remainingTime[i] = mlq.processes[i].BurstTime
    }
    
    currentTime := 0
    completed := 0
    
    for completed < len(mlq.processes) {
        executed := false
        
        // 从高优先级队列开始检查
        for level := 0; level < len(mlq.queues); level++ {
            if len(mlq.queues[level]) > 0 {
                // 执行该级别队列的进程
                processIdx := mlq.queues[level][0]
                mlq.queues[level] = mlq.queues[level][1:]
                
                execTime := mlq.timeQuantum[level]
                if remainingTime[processIdx] < execTime {
                    execTime = remainingTime[processIdx]
                }
                
                currentTime += execTime
                remainingTime[processIdx] -= execTime
                executed = true
                
                if remainingTime[processIdx] == 0 {
                    // 进程完成
                    completed++
                    mlq.processes[processIdx].EndTime = currentTime
                } else {
                    // 进程未完成，降级到下一级队列
                    nextLevel := level + 1
                    if nextLevel >= len(mlq.queues) {
                        nextLevel = len(mlq.queues) - 1
                    }
                    mlq.queues[nextLevel] = append(mlq.queues[nextLevel], processIdx)
                }
                break
            }
        }
        
        if !executed {
            currentTime++
        }
    }
}
```

## 5. 实时调度算法

### 5.1 最早截止时间优先 (EDF)
```go
type RealTimeProcess struct {
    Process
    Deadline int
    Period   int
}

func EDF(processes []RealTimeProcess, totalTime int) {
    currentTime := 0
    
    for currentTime < totalTime {
        // 找到截止时间最早的就绪进程
        earliestIdx := -1
        earliestDeadline := math.MaxInt32
        
        for i := range processes {
            if processes[i].ArrivalTime <= currentTime &&
               processes[i].Deadline < earliestDeadline {
                earliestDeadline = processes[i].Deadline
                earliestIdx = i
            }
        }
        
        if earliestIdx != -1 {
            // 执行选中的进程
            fmt.Printf("时间 %d: 执行进程 %d\n", 
                      currentTime, processes[earliestIdx].ID)
            currentTime++
            
            // 更新进程状态（简化处理）
            processes[earliestIdx].BurstTime--
            if processes[earliestIdx].BurstTime == 0 {
                // 进程完成，生成下一个周期的实例
                processes[earliestIdx].ArrivalTime += processes[earliestIdx].Period
                processes[earliestIdx].Deadline += processes[earliestIdx].Period
                // 重置执行时间
            }
        } else {
            // 没有就绪进程，CPU空闲
            fmt.Printf("时间 %d: CPU空闲\n", currentTime)
            currentTime++
        }
    }
}
```

## 6. 调度算法比较

### 6.1 性能对比
```go
func compareSchedulingAlgorithms() {
    processes := []Process{
        {ID: 1, ArrivalTime: 0, BurstTime: 8},
        {ID: 2, ArrivalTime: 1, BurstTime: 4},
        {ID: 3, ArrivalTime: 2, BurstTime: 9},
        {ID: 4, ArrivalTime: 3, BurstTime: 5},
    }
    
    // 测试FCFS
    fcfsProcesses := make([]Process, len(processes))
    copy(fcfsProcesses, processes)
    FCFS(fcfsProcesses)
    fcfsAvgWait := calculateAverageWaitTime(fcfsProcesses)
    
    // 测试SJF
    sjfProcesses := make([]Process, len(processes))
    copy(sjfProcesses, processes)
    SJF(sjfProcesses)
    sjfAvgWait := calculateAverageWaitTime(sjfProcesses)
    
    // 测试RR
    rrProcesses := make([]Process, len(processes))
    copy(rrProcesses, processes)
    RoundRobin(rrProcesses, 2)
    rrAvgWait := calculateAverageWaitTime(rrProcesses)
    
    fmt.Printf("FCFS平均等待时间: %.2f\n", fcfsAvgWait)
    fmt.Printf("SJF平均等待时间: %.2f\n", sjfAvgWait)
    fmt.Printf("RR平均等待时间: %.2f\n", rrAvgWait)
}

func calculateAverageWaitTime(processes []Process) float64 {
    totalWait := 0
    for _, p := range processes {
        totalWait += p.WaitTime
    }
    return float64(totalWait) / float64(len(processes))
}
```

## 7. 面试重点问题

### Q1: 各种调度算法的优缺点是什么？
**答案**：
- **FCFS**：简单但可能导致护送效应
- **SJF**：平均等待时间最短但可能饥饿
- **RR**：公平但上下文切换开销大
- **优先级**：灵活但可能饥饿

### Q2: 如何选择合适的调度算法？
**答案**：
- **批处理系统**：FCFS、SJF
- **交互式系统**：RR、多级反馈队列
- **实时系统**：EDF、RM
- **通用系统**：多级反馈队列

### Q3: 什么是饥饿现象，如何避免？
**答案**：
- **饥饿**：低优先级进程长时间得不到执行
- **避免方法**：老化技术、公平调度、时间片轮转

### Q4: 抢占式和非抢占式调度的区别？
**答案**：
- **抢占式**：可以中断正在执行的进程，响应性好
- **非抢占式**：进程执行完毕才切换，开销小但响应性差

进程调度是操作系统的核心功能，理解各种调度算法有助于设计高效的系统。
