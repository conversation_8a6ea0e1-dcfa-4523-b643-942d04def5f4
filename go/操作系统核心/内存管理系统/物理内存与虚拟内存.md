# 物理内存与虚拟内存详解

## 1. 内存管理概述

### 1.1 为什么需要内存管理
```
内存管理的目标：
1. 提供抽象 - 给程序员简单的内存模型
2. 提供保护 - 防止进程间相互干扰
3. 提供共享 - 允许进程间安全地共享内存
4. 提供虚拟化 - 让每个进程都认为自己拥有整个内存空间
```

### 1.2 内存层次结构
```
CPU寄存器 (1ns)
    ↓
L1缓存 (1-2ns)
    ↓
L2缓存 (3-10ns)
    ↓
L3缓存 (10-20ns)
    ↓
主内存 (50-100ns)
    ↓
SSD存储 (0.1-1ms)
    ↓
机械硬盘 (5-10ms)
```

## 2. 物理内存管理

### 2.1 物理内存结构
```go
// 物理内存页面结构
type PhysicalPage struct {
    FrameNumber uint64    // 页框号
    RefCount    int       // 引用计数
    Flags       uint32    // 页面标志
    Next        *PhysicalPage // 空闲链表指针
}

// 物理内存管理器
type PhysicalMemoryManager struct {
    totalPages  uint64
    freePages   uint64
    freeList    *PhysicalPage
    pageFrames  []PhysicalPage
    mutex       sync.Mutex
}

func NewPhysicalMemoryManager(totalMemory uint64) *PhysicalMemoryManager {
    pageSize := uint64(4096) // 4KB页面
    totalPages := totalMemory / pageSize
    
    pmm := &PhysicalMemoryManager{
        totalPages: totalPages,
        freePages:  totalPages,
        pageFrames: make([]PhysicalPage, totalPages),
    }
    
    // 初始化空闲链表
    for i := uint64(0); i < totalPages-1; i++ {
        pmm.pageFrames[i].FrameNumber = i
        pmm.pageFrames[i].Next = &pmm.pageFrames[i+1]
    }
    pmm.pageFrames[totalPages-1].FrameNumber = totalPages - 1
    pmm.freeList = &pmm.pageFrames[0]
    
    return pmm
}

// 分配物理页面
func (pmm *PhysicalMemoryManager) AllocatePage() (*PhysicalPage, error) {
    pmm.mutex.Lock()
    defer pmm.mutex.Unlock()
    
    if pmm.freeList == nil {
        return nil, fmt.Errorf("out of memory")
    }
    
    page := pmm.freeList
    pmm.freeList = page.Next
    page.Next = nil
    page.RefCount = 1
    pmm.freePages--
    
    return page, nil
}

// 释放物理页面
func (pmm *PhysicalMemoryManager) FreePage(page *PhysicalPage) {
    pmm.mutex.Lock()
    defer pmm.mutex.Unlock()
    
    page.RefCount--
    if page.RefCount == 0 {
        page.Next = pmm.freeList
        pmm.freeList = page
        pmm.freePages++
    }
}
```

### 2.2 内存分配算法

#### 首次适应算法 (First Fit)
```go
type MemoryBlock struct {
    Start uint64
    Size  uint64
    Free  bool
    Next  *MemoryBlock
}

type FirstFitAllocator struct {
    head *MemoryBlock
    mutex sync.Mutex
}

func (ffa *FirstFitAllocator) Allocate(size uint64) (uint64, error) {
    ffa.mutex.Lock()
    defer ffa.mutex.Unlock()
    
    current := ffa.head
    for current != nil {
        if current.Free && current.Size >= size {
            // 找到合适的块
            if current.Size > size {
                // 分割块
                newBlock := &MemoryBlock{
                    Start: current.Start + size,
                    Size:  current.Size - size,
                    Free:  true,
                    Next:  current.Next,
                }
                current.Next = newBlock
                current.Size = size
            }
            current.Free = false
            return current.Start, nil
        }
        current = current.Next
    }
    
    return 0, fmt.Errorf("no suitable block found")
}
```

#### 最佳适应算法 (Best Fit)
```go
func (bfa *BestFitAllocator) Allocate(size uint64) (uint64, error) {
    bfa.mutex.Lock()
    defer bfa.mutex.Unlock()
    
    var bestBlock *MemoryBlock
    bestSize := uint64(math.MaxUint64)
    
    current := bfa.head
    for current != nil {
        if current.Free && current.Size >= size && current.Size < bestSize {
            bestBlock = current
            bestSize = current.Size
        }
        current = current.Next
    }
    
    if bestBlock == nil {
        return 0, fmt.Errorf("no suitable block found")
    }
    
    // 分配最佳块
    if bestBlock.Size > size {
        newBlock := &MemoryBlock{
            Start: bestBlock.Start + size,
            Size:  bestBlock.Size - size,
            Free:  true,
            Next:  bestBlock.Next,
        }
        bestBlock.Next = newBlock
        bestBlock.Size = size
    }
    bestBlock.Free = false
    
    return bestBlock.Start, nil
}
```

## 3. 虚拟内存管理

### 3.1 虚拟内存概念
```go
// 虚拟地址结构 (32位系统)
type VirtualAddress struct {
    PageNumber uint32 // 页号 (高20位)
    Offset     uint32 // 页内偏移 (低12位)
}

func (va VirtualAddress) GetPageNumber() uint32 {
    return va.PageNumber >> 12
}

func (va VirtualAddress) GetOffset() uint32 {
    return va.PageNumber & 0xFFF
}

// 页表项
type PageTableEntry struct {
    Present    bool   // 存在位
    Writable   bool   // 可写位
    User       bool   // 用户位
    Accessed   bool   // 访问位
    Dirty      bool   // 脏位
    FrameNumber uint32 // 物理页框号
}

// 页表
type PageTable struct {
    entries []PageTableEntry
    size    uint32
}
```

### 3.2 地址转换过程
```go
// 内存管理单元 (MMU)
type MMU struct {
    pageTable *PageTable
    tlb       *TLB // 转换后备缓冲器
}

// 虚拟地址到物理地址转换
func (mmu *MMU) TranslateAddress(virtualAddr uint32) (uint32, error) {
    pageNumber := virtualAddr >> 12
    offset := virtualAddr & 0xFFF
    
    // 首先查看TLB
    if physicalPage, found := mmu.tlb.Lookup(pageNumber); found {
        return (physicalPage << 12) | offset, nil
    }
    
    // TLB未命中，查看页表
    if pageNumber >= uint32(len(mmu.pageTable.entries)) {
        return 0, fmt.Errorf("page number out of range")
    }
    
    pte := mmu.pageTable.entries[pageNumber]
    if !pte.Present {
        // 页面不在内存中，触发页面错误
        return 0, fmt.Errorf("page fault")
    }
    
    // 更新TLB
    mmu.tlb.Insert(pageNumber, pte.FrameNumber)
    
    // 标记访问位
    mmu.pageTable.entries[pageNumber].Accessed = true
    
    physicalAddr := (uint32(pte.FrameNumber) << 12) | offset
    return physicalAddr, nil
}
```

### 3.3 TLB (转换后备缓冲器)
```go
type TLBEntry struct {
    VirtualPage  uint32
    PhysicalPage uint32
    Valid        bool
    Dirty        bool
    LastUsed     time.Time
}

type TLB struct {
    entries []TLBEntry
    size    int
    mutex   sync.RWMutex
}

func NewTLB(size int) *TLB {
    return &TLB{
        entries: make([]TLBEntry, size),
        size:    size,
    }
}

func (tlb *TLB) Lookup(virtualPage uint32) (uint32, bool) {
    tlb.mutex.RLock()
    defer tlb.mutex.RUnlock()
    
    for i := 0; i < tlb.size; i++ {
        if tlb.entries[i].Valid && tlb.entries[i].VirtualPage == virtualPage {
            tlb.entries[i].LastUsed = time.Now()
            return tlb.entries[i].PhysicalPage, true
        }
    }
    
    return 0, false
}

func (tlb *TLB) Insert(virtualPage, physicalPage uint32) {
    tlb.mutex.Lock()
    defer tlb.mutex.Unlock()
    
    // 查找空闲或最久未使用的条目
    oldestIdx := 0
    oldestTime := tlb.entries[0].LastUsed
    
    for i := 0; i < tlb.size; i++ {
        if !tlb.entries[i].Valid {
            oldestIdx = i
            break
        }
        if tlb.entries[i].LastUsed.Before(oldestTime) {
            oldestIdx = i
            oldestTime = tlb.entries[i].LastUsed
        }
    }
    
    tlb.entries[oldestIdx] = TLBEntry{
        VirtualPage:  virtualPage,
        PhysicalPage: physicalPage,
        Valid:        true,
        LastUsed:     time.Now(),
    }
}
```

## 4. 页面置换算法

### 4.1 FIFO算法
```go
type FIFOReplacer struct {
    pages []uint32
    size  int
    front int
    rear  int
    count int
}

func (fifo *FIFOReplacer) ReplacePage(newPage uint32) uint32 {
    if fifo.count < fifo.size {
        // 还有空闲位置
        fifo.pages[fifo.rear] = newPage
        fifo.rear = (fifo.rear + 1) % fifo.size
        fifo.count++
        return 0 // 没有页面被替换
    }
    
    // 替换最早进入的页面
    replacedPage := fifo.pages[fifo.front]
    fifo.pages[fifo.front] = newPage
    fifo.front = (fifo.front + 1) % fifo.size
    
    return replacedPage
}
```

### 4.2 LRU算法
```go
type LRUReplacer struct {
    pages    map[uint32]*list.Element
    lruList  *list.List
    capacity int
}

func NewLRUReplacer(capacity int) *LRUReplacer {
    return &LRUReplacer{
        pages:    make(map[uint32]*list.Element),
        lruList:  list.New(),
        capacity: capacity,
    }
}

func (lru *LRUReplacer) AccessPage(page uint32) {
    if elem, exists := lru.pages[page]; exists {
        // 页面已存在，移到前面
        lru.lruList.MoveToFront(elem)
    } else {
        // 新页面
        if lru.lruList.Len() >= lru.capacity {
            // 需要替换
            oldest := lru.lruList.Back()
            if oldest != nil {
                lru.lruList.Remove(oldest)
                delete(lru.pages, oldest.Value.(uint32))
            }
        }
        
        elem := lru.lruList.PushFront(page)
        lru.pages[page] = elem
    }
}

func (lru *LRUReplacer) GetVictim() uint32 {
    if lru.lruList.Len() == 0 {
        return 0
    }
    
    oldest := lru.lruList.Back()
    return oldest.Value.(uint32)
}
```

### 4.3 时钟算法
```go
type ClockReplacer struct {
    pages []ClockPage
    hand  int
    size  int
}

type ClockPage struct {
    PageNumber uint32
    ReferenceBit bool
    Valid      bool
}

func (clock *ClockReplacer) ReplacePage(newPage uint32) uint32 {
    for {
        current := &clock.pages[clock.hand]
        
        if !current.Valid {
            // 空闲位置
            current.PageNumber = newPage
            current.ReferenceBit = true
            current.Valid = true
            clock.hand = (clock.hand + 1) % clock.size
            return 0
        }
        
        if current.ReferenceBit {
            // 给第二次机会
            current.ReferenceBit = false
        } else {
            // 替换这个页面
            replacedPage := current.PageNumber
            current.PageNumber = newPage
            current.ReferenceBit = true
            clock.hand = (clock.hand + 1) % clock.size
            return replacedPage
        }
        
        clock.hand = (clock.hand + 1) % clock.size
    }
}
```

## 5. 内存映射

### 5.1 mmap系统调用
```go
// 模拟mmap功能
type MemoryMapping struct {
    VirtualAddr uint64
    Size        uint64
    Protection  int
    Flags       int
    FileDesc    int
    Offset      uint64
}

const (
    PROT_READ  = 1
    PROT_WRITE = 2
    PROT_EXEC  = 4
    
    MAP_PRIVATE   = 1
    MAP_SHARED    = 2
    MAP_ANONYMOUS = 4
)

func Mmap(addr uint64, length uint64, prot int, flags int, fd int, offset uint64) (uint64, error) {
    // 分配虚拟地址空间
    virtualAddr := allocateVirtualAddress(addr, length)
    if virtualAddr == 0 {
        return 0, fmt.Errorf("cannot allocate virtual address")
    }
    
    mapping := &MemoryMapping{
        VirtualAddr: virtualAddr,
        Size:        length,
        Protection:  prot,
        Flags:       flags,
        FileDesc:    fd,
        Offset:      offset,
    }
    
    // 建立映射关系
    if err := establishMapping(mapping); err != nil {
        return 0, err
    }
    
    return virtualAddr, nil
}

func allocateVirtualAddress(hint uint64, size uint64) uint64 {
    // 简化实现：在虚拟地址空间中找到合适的区域
    // 实际实现需要考虑地址空间布局、对齐等
    return hint // 简化返回
}

func establishMapping(mapping *MemoryMapping) error {
    // 建立虚拟地址到物理地址的映射
    // 如果是文件映射，需要处理文件内容
    // 如果是匿名映射，分配物理页面
    return nil
}
```

## 6. 内存保护

### 6.1 段保护机制
```go
type SegmentDescriptor struct {
    Base     uint32 // 段基址
    Limit    uint32 // 段限长
    Type     uint8  // 段类型
    DPL      uint8  // 描述符特权级
    Present  bool   // 存在位
    Readable bool   // 可读位
    Writable bool   // 可写位
    Executable bool // 可执行位
}

func (sd *SegmentDescriptor) CheckAccess(offset uint32, accessType int) error {
    if !sd.Present {
        return fmt.Errorf("segment not present")
    }
    
    if offset > sd.Limit {
        return fmt.Errorf("segment limit exceeded")
    }
    
    switch accessType {
    case ACCESS_READ:
        if !sd.Readable {
            return fmt.Errorf("segment not readable")
        }
    case ACCESS_WRITE:
        if !sd.Writable {
            return fmt.Errorf("segment not writable")
        }
    case ACCESS_EXECUTE:
        if !sd.Executable {
            return fmt.Errorf("segment not executable")
        }
    }
    
    return nil
}
```

## 7. 面试重点问题

### Q1: 虚拟内存的优势是什么？
**答案**：
- **地址空间隔离**：每个进程有独立的地址空间
- **内存保护**：防止进程间相互干扰
- **内存共享**：多个进程可以共享同一物理内存
- **按需分配**：只在需要时分配物理内存

### Q2: 页面置换算法的比较？
**答案**：
- **FIFO**：简单但可能出现Belady异常
- **LRU**：性能好但实现复杂
- **时钟算法**：LRU的近似，实现简单

### Q3: TLB的作用是什么？
**答案**：
- **加速地址转换**：缓存最近使用的页表项
- **减少内存访问**：避免每次都访问页表
- **提高性能**：显著提升内存访问速度

### Q4: 什么是内存碎片，如何解决？
**答案**：
- **内部碎片**：分配单元内部的浪费
- **外部碎片**：空闲内存块太小无法使用
- **解决方法**：内存压缩、伙伴系统、slab分配器

内存管理是操作系统的核心功能，理解物理内存和虚拟内存的工作原理对系统编程至关重要。
