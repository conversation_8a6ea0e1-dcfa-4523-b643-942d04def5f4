MongoDB的聚合管道是一个强大的数据处理框架，它允许开发者通过一系列阶段来处理和转换数据。理解聚合管道的工作原理和优化策略对于构建高性能的MongoDB应用至关重要。

### 1. **聚合管道的基本概念**

#### **聚合管道的组成**
```javascript
// 聚合管道的基本结构
db.collection.aggregate([
    { $match: { ... } },      // 过滤阶段
    { $group: { ... } },      // 分组阶段
    { $sort: { ... } },       // 排序阶段
    { $project: { ... } },    // 投影阶段
    { $limit: 10 }            // 限制阶段
])
```

#### **Go语言中的聚合操作**
```go
package main

import (
    "context"
    "fmt"
    "go.mongodb.org/mongo-driver/bson"
    "go.mongodb.org/mongo-driver/mongo"
    "go.mongodb.org/mongo-driver/mongo/options"
)

type Order struct {
    ID         primitive.ObjectID `bson:"_id,omitempty"`
    UserID     string            `bson:"user_id"`
    ProductID  string            `bson:"product_id"`
    Amount     float64           `bson:"amount"`
    Status     string            `bson:"status"`
    CreatedAt  time.Time         `bson:"created_at"`
}

type OrderStats struct {
    UserID      string  `bson:"_id"`
    TotalAmount float64 `bson:"total_amount"`
    OrderCount  int     `bson:"order_count"`
    AvgAmount   float64 `bson:"avg_amount"`
}

func AggregateOrderStats(collection *mongo.Collection) ([]OrderStats, error) {
    pipeline := []bson.M{
        // 阶段1：过滤已完成的订单
        {
            "$match": bson.M{
                "status": "completed",
                "created_at": bson.M{
                    "$gte": time.Now().AddDate(0, -1, 0), // 最近一个月
                },
            },
        },
        // 阶段2：按用户分组并计算统计信息
        {
            "$group": bson.M{
                "_id": "$user_id",
                "total_amount": bson.M{"$sum": "$amount"},
                "order_count":  bson.M{"$sum": 1},
                "avg_amount":   bson.M{"$avg": "$amount"},
                "max_amount":   bson.M{"$max": "$amount"},
                "min_amount":   bson.M{"$min": "$amount"},
            },
        },
        // 阶段3：过滤订单数量大于5的用户
        {
            "$match": bson.M{
                "order_count": bson.M{"$gt": 5},
            },
        },
        // 阶段4：按总金额降序排序
        {
            "$sort": bson.M{
                "total_amount": -1,
            },
        },
        // 阶段5：限制返回前10个用户
        {
            "$limit": 10,
        },
        // 阶段6：添加计算字段
        {
            "$addFields": bson.M{
                "user_level": bson.M{
                    "$switch": bson.M{
                        "branches": []bson.M{
                            {
                                "case": bson.M{"$gte": []interface{}{"$total_amount", 10000}},
                                "then": "VIP",
                            },
                            {
                                "case": bson.M{"$gte": []interface{}{"$total_amount", 5000}},
                                "then": "Gold",
                            },
                        },
                        "default": "Silver",
                    },
                },
            },
        },
    }
    
    cursor, err := collection.Aggregate(context.Background(), pipeline)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(context.Background())
    
    var results []OrderStats
    if err = cursor.All(context.Background(), &results); err != nil {
        return nil, err
    }
    
    return results, nil
}
```

### 2. **常用聚合阶段详解**

#### **$match阶段优化**
```go
// 优化前：在$group后使用$match
func BadAggregation(collection *mongo.Collection) {
    pipeline := []bson.M{
        {"$group": bson.M{
            "_id": "$category",
            "total": bson.M{"$sum": "$amount"},
        }},
        {"$match": bson.M{
            "total": bson.M{"$gt": 1000},
        }},
    }
    // 这样会处理所有文档后再过滤
}

// 优化后：尽早使用$match
func GoodAggregation(collection *mongo.Collection) {
    pipeline := []bson.M{
        // 先过滤，减少后续处理的数据量
        {"$match": bson.M{
            "status": "active",
            "created_at": bson.M{"$gte": time.Now().AddDate(0, -1, 0)},
        }},
        {"$group": bson.M{
            "_id": "$category",
            "total": bson.M{"$sum": "$amount"},
        }},
        {"$match": bson.M{
            "total": bson.M{"$gt": 1000},
        }},
    }
}
```

#### **$lookup阶段（关联查询）**
```go
type UserWithOrders struct {
    ID     primitive.ObjectID `bson:"_id"`
    Name   string            `bson:"name"`
    Email  string            `bson:"email"`
    Orders []Order           `bson:"orders"`
}

func GetUsersWithOrders(userCollection, orderCollection *mongo.Collection) ([]UserWithOrders, error) {
    pipeline := []bson.M{
        // 关联订单数据
        {
            "$lookup": bson.M{
                "from":         "orders",
                "localField":   "_id",
                "foreignField": "user_id",
                "as":           "orders",
                // 使用pipeline进行更复杂的关联
                "pipeline": []bson.M{
                    {
                        "$match": bson.M{
                            "status": "completed",
                        },
                    },
                    {
                        "$sort": bson.M{
                            "created_at": -1,
                        },
                    },
                    {
                        "$limit": 5, // 只获取最近5个订单
                    },
                },
            },
        },
        // 只返回有订单的用户
        {
            "$match": bson.M{
                "orders": bson.M{"$ne": []interface{}{}},
            },
        },
        // 添加订单统计信息
        {
            "$addFields": bson.M{
                "order_count": bson.M{"$size": "$orders"},
                "total_spent": bson.M{"$sum": "$orders.amount"},
            },
        },
    }
    
    cursor, err := userCollection.Aggregate(context.Background(), pipeline)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(context.Background())
    
    var results []UserWithOrders
    if err = cursor.All(context.Background(), &results); err != nil {
        return nil, err
    }
    
    return results, nil
}
```

#### **$facet阶段（多维度聚合）**
```go
type DashboardStats struct {
    SalesByCategory []CategorySales `bson:"sales_by_category"`
    SalesByMonth    []MonthlySales  `bson:"sales_by_month"`
    TopProducts     []ProductSales  `bson:"top_products"`
    Summary         []Summary       `bson:"summary"`
}

func GetDashboardStats(collection *mongo.Collection) (*DashboardStats, error) {
    pipeline := []bson.M{
        {
            "$match": bson.M{
                "created_at": bson.M{
                    "$gte": time.Now().AddDate(-1, 0, 0), // 最近一年
                },
            },
        },
        {
            "$facet": bson.M{
                // 按类别统计销售额
                "sales_by_category": []bson.M{
                    {
                        "$group": bson.M{
                            "_id":   "$category",
                            "total": bson.M{"$sum": "$amount"},
                            "count": bson.M{"$sum": 1},
                        },
                    },
                    {
                        "$sort": bson.M{"total": -1},
                    },
                },
                // 按月份统计销售额
                "sales_by_month": []bson.M{
                    {
                        "$group": bson.M{
                            "_id": bson.M{
                                "year":  bson.M{"$year": "$created_at"},
                                "month": bson.M{"$month": "$created_at"},
                            },
                            "total": bson.M{"$sum": "$amount"},
                            "count": bson.M{"$sum": 1},
                        },
                    },
                    {
                        "$sort": bson.M{"_id": 1},
                    },
                },
                // 热销产品
                "top_products": []bson.M{
                    {
                        "$group": bson.M{
                            "_id":   "$product_id",
                            "total": bson.M{"$sum": "$amount"},
                            "count": bson.M{"$sum": 1},
                        },
                    },
                    {
                        "$sort": bson.M{"count": -1},
                    },
                    {
                        "$limit": 10,
                    },
                },
                // 总体统计
                "summary": []bson.M{
                    {
                        "$group": bson.M{
                            "_id":         nil,
                            "total_sales": bson.M{"$sum": "$amount"},
                            "total_orders": bson.M{"$sum": 1},
                            "avg_order":   bson.M{"$avg": "$amount"},
                        },
                    },
                },
            },
        },
    }
    
    cursor, err := collection.Aggregate(context.Background(), pipeline)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(context.Background())
    
    var results []DashboardStats
    if err = cursor.All(context.Background(), &results); err != nil {
        return nil, err
    }
    
    if len(results) > 0 {
        return &results[0], nil
    }
    return nil, nil
}
```

### 3. **聚合管道性能优化策略**

#### **索引优化**
```go
// 为聚合管道创建合适的索引
func CreateAggregationIndexes(collection *mongo.Collection) error {
    indexes := []mongo.IndexModel{
        // 复合索引支持$match和$sort
        {
            Keys: bson.D{
                {"status", 1},
                {"created_at", -1},
            },
        },
        // 支持$group操作的索引
        {
            Keys: bson.D{
                {"user_id", 1},
                {"amount", 1},
            },
        },
        // 支持$lookup操作的索引
        {
            Keys: bson.D{
                {"product_id", 1},
            },
        },
    }
    
    _, err := collection.Indexes().CreateMany(context.Background(), indexes)
    return err
}

// 使用explain分析聚合性能
func ExplainAggregation(collection *mongo.Collection, pipeline []bson.M) {
    opts := options.Aggregate().SetExplain(true)
    cursor, err := collection.Aggregate(context.Background(), pipeline, opts)
    if err != nil {
        log.Fatal(err)
    }
    defer cursor.Close(context.Background())
    
    var explanation bson.M
    if cursor.Next(context.Background()) {
        cursor.Decode(&explanation)
        fmt.Printf("Aggregation Explanation: %+v\n", explanation)
    }
}
```

#### **内存使用优化**
```go
// 使用allowDiskUse选项处理大数据集
func LargeDataAggregation(collection *mongo.Collection) ([]bson.M, error) {
    pipeline := []bson.M{
        {"$match": bson.M{"status": "active"}},
        {"$group": bson.M{
            "_id": "$category",
            "total": bson.M{"$sum": "$amount"},
            "docs": bson.M{"$push": "$$ROOT"}, // 可能消耗大量内存
        }},
        {"$sort": bson.M{"total": -1}},
    }
    
    // 允许使用磁盘存储中间结果
    opts := options.Aggregate().SetAllowDiskUse(true)
    
    cursor, err := collection.Aggregate(context.Background(), pipeline, opts)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(context.Background())
    
    var results []bson.M
    if err = cursor.All(context.Background(), &results); err != nil {
        return nil, err
    }
    
    return results, nil
}

// 分批处理大数据集
func BatchAggregation(collection *mongo.Collection, batchSize int) error {
    pipeline := []bson.M{
        {"$match": bson.M{"processed": false}},
        {"$limit": batchSize},
    }
    
    for {
        cursor, err := collection.Aggregate(context.Background(), pipeline)
        if err != nil {
            return err
        }
        
        var batch []bson.M
        if err = cursor.All(context.Background(), &batch); err != nil {
            cursor.Close(context.Background())
            return err
        }
        cursor.Close(context.Background())
        
        if len(batch) == 0 {
            break // 没有更多数据
        }
        
        // 处理批次数据
        if err := processBatch(collection, batch); err != nil {
            return err
        }
    }
    
    return nil
}
```

### 4. **高级聚合技巧**

#### **时间序列数据聚合**
```go
type TimeSeriesData struct {
    Timestamp time.Time `bson:"timestamp"`
    Value     float64   `bson:"value"`
    Sensor    string    `bson:"sensor"`
}

func AggregateTimeSeriesData(collection *mongo.Collection, 
    interval string) ([]bson.M, error) {
    
    var groupBy bson.M
    switch interval {
    case "hour":
        groupBy = bson.M{
            "year":  bson.M{"$year": "$timestamp"},
            "month": bson.M{"$month": "$timestamp"},
            "day":   bson.M{"$dayOfMonth": "$timestamp"},
            "hour":  bson.M{"$hour": "$timestamp"},
        }
    case "day":
        groupBy = bson.M{
            "year":  bson.M{"$year": "$timestamp"},
            "month": bson.M{"$month": "$timestamp"},
            "day":   bson.M{"$dayOfMonth": "$timestamp"},
        }
    case "month":
        groupBy = bson.M{
            "year":  bson.M{"$year": "$timestamp"},
            "month": bson.M{"$month": "$timestamp"},
        }
    }
    
    pipeline := []bson.M{
        {
            "$match": bson.M{
                "timestamp": bson.M{
                    "$gte": time.Now().AddDate(0, -1, 0),
                },
            },
        },
        {
            "$group": bson.M{
                "_id":     groupBy,
                "avg":     bson.M{"$avg": "$value"},
                "max":     bson.M{"$max": "$value"},
                "min":     bson.M{"$min": "$value"},
                "count":   bson.M{"$sum": 1},
                "sensors": bson.M{"$addToSet": "$sensor"},
            },
        },
        {
            "$sort": bson.M{"_id": 1},
        },
    }
    
    cursor, err := collection.Aggregate(context.Background(), pipeline)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(context.Background())
    
    var results []bson.M
    if err = cursor.All(context.Background(), &results); err != nil {
        return nil, err
    }
    
    return results, nil
}
```

#### **地理空间聚合**
```go
type Location struct {
    Type        string    `bson:"type"`
    Coordinates []float64 `bson:"coordinates"`
}

type Store struct {
    ID       primitive.ObjectID `bson:"_id,omitempty"`
    Name     string            `bson:"name"`
    Location Location          `bson:"location"`
    Sales    float64           `bson:"sales"`
}

func AggregateNearbyStores(collection *mongo.Collection, 
    centerLng, centerLat float64, maxDistance int) ([]bson.M, error) {
    
    pipeline := []bson.M{
        // 地理空间查询
        {
            "$geoNear": bson.M{
                "near": bson.M{
                    "type":        "Point",
                    "coordinates": []float64{centerLng, centerLat},
                },
                "distanceField": "distance",
                "maxDistance":   maxDistance,
                "spherical":     true,
            },
        },
        // 按距离分组统计
        {
            "$bucket": bson.M{
                "groupBy": "$distance",
                "boundaries": []int{0, 1000, 5000, 10000, maxDistance},
                "default": "far",
                "output": bson.M{
                    "count":      bson.M{"$sum": 1},
                    "totalSales": bson.M{"$sum": "$sales"},
                    "avgSales":   bson.M{"$avg": "$sales"},
                    "stores":     bson.M{"$push": "$name"},
                },
            },
        },
    }
    
    cursor, err := collection.Aggregate(context.Background(), pipeline)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(context.Background())
    
    var results []bson.M
    if err = cursor.All(context.Background(), &results); err != nil {
        return nil, err
    }
    
    return results, nil
}
```

### 5. **聚合管道监控和调试**

#### **性能监控**
```go
type AggregationProfiler struct {
    collection *mongo.Collection
    logger     *log.Logger
}

func (ap *AggregationProfiler) ProfiledAggregate(pipeline []bson.M, 
    opts ...*options.AggregateOptions) (*mongo.Cursor, time.Duration, error) {
    
    start := time.Now()
    
    cursor, err := ap.collection.Aggregate(context.Background(), pipeline, opts...)
    
    duration := time.Since(start)
    
    // 记录性能日志
    ap.logger.Printf("Aggregation completed in %v, pipeline stages: %d", 
        duration, len(pipeline))
    
    // 如果执行时间过长，记录详细信息
    if duration > time.Second*5 {
        ap.logger.Printf("Slow aggregation detected: %+v", pipeline)
    }
    
    return cursor, duration, err
}

// 聚合管道性能分析
func AnalyzeAggregationPerformance(collection *mongo.Collection, 
    pipeline []bson.M) (*AggregationStats, error) {
    
    // 启用profiling
    db := collection.Database()
    db.RunCommand(context.Background(), bson.M{"profile": 2})
    
    start := time.Now()
    cursor, err := collection.Aggregate(context.Background(), pipeline)
    if err != nil {
        return nil, err
    }
    
    // 消费所有结果以获得完整的执行时间
    var results []bson.M
    err = cursor.All(context.Background(), &results)
    cursor.Close(context.Background())
    
    duration := time.Since(start)
    
    // 获取profiling信息
    profileColl := db.Collection("system.profile")
    profileCursor, err := profileColl.Find(context.Background(), 
        bson.M{"ts": bson.M{"$gte": start}})
    if err != nil {
        return nil, err
    }
    defer profileCursor.Close(context.Background())
    
    var profiles []bson.M
    profileCursor.All(context.Background(), &profiles)
    
    return &AggregationStats{
        Duration:     duration,
        ResultCount:  len(results),
        ProfileData:  profiles,
    }, nil
}

type AggregationStats struct {
    Duration    time.Duration `json:"duration"`
    ResultCount int          `json:"result_count"`
    ProfileData []bson.M     `json:"profile_data"`
}
```

### 6. **面试常见问题**

#### **问题1：聚合管道vs普通查询的选择**
```go
// 简单查询：使用Find
func SimpleQuery(collection *mongo.Collection) ([]Order, error) {
    filter := bson.M{"status": "completed"}
    cursor, err := collection.Find(context.Background(), filter)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(context.Background())
    
    var orders []Order
    err = cursor.All(context.Background(), &orders)
    return orders, err
}

// 复杂统计：使用聚合管道
func ComplexAggregation(collection *mongo.Collection) ([]bson.M, error) {
    pipeline := []bson.M{
        {"$match": bson.M{"status": "completed"}},
        {"$group": bson.M{
            "_id": "$user_id",
            "total": bson.M{"$sum": "$amount"},
        }},
        {"$sort": bson.M{"total": -1}},
    }
    
    cursor, err := collection.Aggregate(context.Background(), pipeline)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(context.Background())
    
    var results []bson.M
    err = cursor.All(context.Background(), &results)
    return results, err
}
```

#### **问题2：聚合管道的内存限制**
- 单个聚合阶段默认内存限制：100MB
- 解决方案：使用`allowDiskUse: true`
- 优化策略：尽早过滤数据，避免不必要的字段

#### **问题3：聚合管道的执行顺序优化**
- `$match`和`$sort`会被优化器前移
- `$project`可以减少后续阶段的数据量
- `$limit`应该尽早使用

### 总结

MongoDB聚合管道是一个强大的数据处理工具，合理使用可以：

1. **提高查询性能**：通过索引优化和阶段顺序优化
2. **减少网络传输**：在数据库层面完成复杂计算
3. **简化应用逻辑**：将数据处理逻辑下推到数据库
4. **支持复杂分析**：实现多维度数据统计和分析

在使用聚合管道时，需要注意：
- **索引设计**：为聚合操作创建合适的索引
- **内存管理**：合理使用allowDiskUse选项
- **性能监控**：定期分析聚合性能
- **阶段优化**：合理安排聚合阶段顺序

掌握聚合管道的使用和优化技巧，能够显著提升MongoDB应用的性能和开发效率。
